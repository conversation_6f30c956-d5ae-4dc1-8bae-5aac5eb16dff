{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "dirpath = \"/Users/<USER>/Downloads\"\n", "sys.path.append(dirpath)\n", "from pylib.dnn_fix_v2 import *"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense Weight Diff: \n", "                                                  name   origin_shape  current_shape    type\n", "0                  QUEUE_SOFT/share_bottom_layer_0/w:0   (7821, 1024)   (8013, 1024)  change\n", "1                good_click_cate2cate_dnn_layer/beta:0      (3000, 1)      (4200, 1)  change\n", "2               good_click_cate2cate_dnn_layer/gamma:0      (3000, 1)      (4200, 1)  change\n", "3                 good_click_cate2cate_layer_new_0/w:0      (60, 128)      (84, 128)  change\n", "4                         share_bottom_layer_new_0/w:0  (13835, 1024)  (14027, 1024)  change\n", "5    attention_layer/good_show_user_embedding_atten...       (48, 32)           None  delete\n", "6    attention_layer/good_show_user_embedding_atten...      (288, 32)           None  delete\n", "7    attention_layer/good_show_user_embedding_atten...       (48, 32)           None  delete\n", "8    attention_layer/good_show_user_embedding_pooli...       (256, 1)           None  delete\n", "9    attention_layer/good_show_user_embedding_pooli...       (256, 1)           None  delete\n", "10   attention_layer/good_show_user_embedding_pooli...       (256, 1)           None  delete\n", "11   attention_layer/good_show_user_embedding_pooli...     (336, 256)           None  delete\n", "12   attention_layer/good_show_user_embedding_pooli...        (32, 1)           None  delete\n", "13   attention_layer/good_show_user_embedding_pooli...      (256, 32)           None  delete\n", "14   attention_layer/long_seq_suffix_pooling_0/Laye...       (256, 1)           None  delete\n", "15   attention_layer/long_seq_suffix_pooling_0/Laye...       (256, 1)           None  delete\n", "16       attention_layer/long_seq_suffix_pooling_0/b:0       (256, 1)           None  delete\n", "17       attention_layer/long_seq_suffix_pooling_0/w:0    (1080, 256)           None  delete\n", "18       attention_layer/long_seq_suffix_pooling_1/b:0        (32, 1)           None  delete\n", "19       attention_layer/long_seq_suffix_pooling_1/w:0      (256, 32)           None  delete\n", "20                                 LayerNorm_35/beta:0           None        (32, 1)     add\n", "21                                LayerNorm_35/gamma:0           None        (32, 1)     add\n", "22                                 LayerNorm_36/beta:0           None        (32, 1)     add\n", "23                                LayerNorm_36/gamma:0           None        (32, 1)     add\n", "24                                 LayerNorm_37/beta:0           None        (32, 1)     add\n", "25                                LayerNorm_37/gamma:0           None        (32, 1)     add\n", "26                                 LayerNorm_38/beta:0           None        (32, 1)     add\n", "27                                LayerNorm_38/gamma:0           None        (32, 1)     add\n", "28                                 LayerNorm_39/beta:0           None        (32, 1)     add\n", "29                                LayerNorm_39/gamma:0           None        (32, 1)     add\n", "30                                 LayerNorm_40/beta:0           None        (32, 1)     add\n", "31                                LayerNorm_40/gamma:0           None        (32, 1)     add\n", "32                                 LayerNorm_41/beta:0           None        (32, 1)     add\n", "33                                LayerNorm_41/gamma:0           None        (32, 1)     add\n", "34                                 LayerNorm_42/beta:0           None        (32, 1)     add\n", "35                                LayerNorm_42/gamma:0           None        (32, 1)     add\n", "36                                 LayerNorm_43/beta:0           None        (32, 1)     add\n", "37                                LayerNorm_43/gamma:0           None        (32, 1)     add\n", "38                                 LayerNorm_44/beta:0           None        (32, 1)     add\n", "39                                LayerNorm_44/gamma:0           None        (32, 1)     add\n", "40                                 LayerNorm_45/beta:0           None        (32, 1)     add\n", "41                                LayerNorm_45/gamma:0           None        (32, 1)     add\n", "42                                 LayerNorm_46/beta:0           None        (32, 1)     add\n", "43                                LayerNorm_46/gamma:0           None        (32, 1)     add\n", "44                                 LayerNorm_47/beta:0           None        (32, 1)     add\n", "45                                LayerNorm_47/gamma:0           None        (32, 1)     add\n", "46                                 LayerNorm_48/beta:0           None        (32, 1)     add\n", "47                                LayerNorm_48/gamma:0           None        (32, 1)     add\n", "48                                 LayerNorm_49/beta:0           None        (32, 1)     add\n", "49                                LayerNorm_49/gamma:0           None        (32, 1)     add\n", "50                                 LayerNorm_50/beta:0           None        (32, 1)     add\n", "51                                LayerNorm_50/gamma:0           None        (32, 1)     add\n", "52                                 LayerNorm_51/beta:0           None        (32, 1)     add\n", "53                                LayerNorm_51/gamma:0           None        (32, 1)     add\n", "54                                 LayerNorm_52/beta:0           None        (32, 1)     add\n", "55                                LayerNorm_52/gamma:0           None        (32, 1)     add\n", "56                                 LayerNorm_53/beta:0           None        (32, 1)     add\n", "57                                LayerNorm_53/gamma:0           None        (32, 1)     add\n", "58                                 LayerNorm_54/beta:0           None        (32, 1)     add\n", "59                                LayerNorm_54/gamma:0           None        (32, 1)     add\n", "60                                 LayerNorm_55/beta:0           None        (32, 1)     add\n", "61                                LayerNorm_55/gamma:0           None        (32, 1)     add\n", "62                                 LayerNorm_56/beta:0           None        (32, 1)     add\n", "63                                LayerNorm_56/gamma:0           None        (32, 1)     add\n", "64                                 LayerNorm_57/beta:0           None        (32, 1)     add\n", "65                                LayerNorm_57/gamma:0           None        (32, 1)     add\n", "66                                 LayerNorm_58/beta:0           None        (32, 1)     add\n", "67                                LayerNorm_58/gamma:0           None        (32, 1)     add\n", "68                                 LayerNorm_59/beta:0           None        (32, 1)     add\n", "69                                LayerNorm_59/gamma:0           None        (32, 1)     add\n", "70                                 LayerNorm_60/beta:0           None        (32, 1)     add\n", "71                                LayerNorm_60/gamma:0           None        (32, 1)     add\n", "72                                 LayerNorm_61/beta:0           None        (32, 1)     add\n", "73                                LayerNorm_61/gamma:0           None        (32, 1)     add\n", "74                                 LayerNorm_62/beta:0           None        (32, 1)     add\n", "75                                LayerNorm_62/gamma:0           None        (32, 1)     add\n", "76                                 LayerNorm_63/beta:0           None        (32, 1)     add\n", "77                                LayerNorm_63/gamma:0           None        (32, 1)     add\n", "78                                 LayerNorm_64/beta:0           None        (32, 1)     add\n", "79                                LayerNorm_64/gamma:0           None        (32, 1)     add\n", "80                                 LayerNorm_65/beta:0           None        (32, 1)     add\n", "81                                LayerNorm_65/gamma:0           None        (32, 1)     add\n", "82                                 LayerNorm_66/beta:0           None        (32, 1)     add\n", "83                                LayerNorm_66/gamma:0           None        (32, 1)     add\n", "84                                 LayerNorm_67/beta:0           None        (32, 1)     add\n", "85                                LayerNorm_67/gamma:0           None        (32, 1)     add\n", "86                                 LayerNorm_68/beta:0           None        (32, 1)     add\n", "87                                LayerNorm_68/gamma:0           None        (32, 1)     add\n", "88                                 LayerNorm_69/beta:0           None        (32, 1)     add\n", "89                                LayerNorm_69/gamma:0           None        (32, 1)     add\n", "90                                 LayerNorm_70/beta:0           None        (32, 1)     add\n", "91                                LayerNorm_70/gamma:0           None        (32, 1)     add\n", "92                                 LayerNorm_71/beta:0           None        (32, 1)     add\n", "93                                LayerNorm_71/gamma:0           None        (32, 1)     add\n", "94                                 LayerNorm_72/beta:0           None        (32, 1)     add\n", "95                                LayerNorm_72/gamma:0           None        (32, 1)     add\n", "96                                 LayerNorm_73/beta:0           None        (32, 1)     add\n", "97                                LayerNorm_73/gamma:0           None        (32, 1)     add\n", "98                                 LayerNorm_74/beta:0           None        (32, 1)     add\n", "99                                LayerNorm_74/gamma:0           None        (32, 1)     add\n", "100                                LayerNorm_75/beta:0           None        (32, 1)     add\n", "101                               LayerNorm_75/gamma:0           None        (32, 1)     add\n", "102                                LayerNorm_76/beta:0           None        (32, 1)     add\n", "103                               LayerNorm_76/gamma:0           None        (32, 1)     add\n", "104                                LayerNorm_77/beta:0           None        (32, 1)     add\n", "105                               LayerNorm_77/gamma:0           None        (32, 1)     add\n", "106                                LayerNorm_78/beta:0           None        (32, 1)     add\n", "107                               LayerNorm_78/gamma:0           None        (32, 1)     add\n", "108                                LayerNorm_79/beta:0           None        (32, 1)     add\n", "109                               LayerNorm_79/gamma:0           None        (32, 1)     add\n", "110                                LayerNorm_80/beta:0           None        (32, 1)     add\n", "111                               LayerNorm_80/gamma:0           None        (32, 1)     add\n", "112                                LayerNorm_81/beta:0           None        (32, 1)     add\n", "113                               LayerNorm_81/gamma:0           None        (32, 1)     add\n", "114                                LayerNorm_82/beta:0           None        (32, 1)     add\n", "115                               LayerNorm_82/gamma:0           None        (32, 1)     add\n", "116                                LayerNorm_83/beta:0           None        (32, 1)     add\n", "117                               LayerNorm_83/gamma:0           None        (32, 1)     add\n", "118                                LayerNorm_84/beta:0           None        (32, 1)     add\n", "119                               LayerNorm_84/gamma:0           None        (32, 1)     add\n", "120                                LayerNorm_85/beta:0           None        (32, 1)     add\n", "121                               LayerNorm_85/gamma:0           None        (32, 1)     add\n", "122                                LayerNorm_86/beta:0           None        (32, 1)     add\n", "123                               LayerNorm_86/gamma:0           None        (32, 1)     add\n", "124                                LayerNorm_87/beta:0           None        (32, 1)     add\n", "125                               LayerNorm_87/gamma:0           None        (32, 1)     add\n", "126                                LayerNorm_88/beta:0           None        (32, 1)     add\n", "127                               LayerNorm_88/gamma:0           None        (32, 1)     add\n", "128                  cart_photo_exposure_aid_fake0/b:0           None        (64, 1)     add\n", "129                  cart_photo_exposure_aid_fake0/w:0           None      (400, 64)     add\n", "130                  cart_photo_exposure_aid_fake1/b:0           None        (32, 1)     add\n", "131                  cart_photo_exposure_aid_fake1/w:0           None       (64, 32)     add\n", "132             cart_photo_exposure_category_fake0/b:0           None        (64, 1)     add\n", "133             cart_photo_exposure_category_fake0/w:0           None      (400, 64)     add\n", "134             cart_photo_exposure_category_fake1/b:0           None        (32, 1)     add\n", "135             cart_photo_exposure_category_fake1/w:0           None       (64, 32)     add\n", "136              cart_photo_exposure_channel_fake0/b:0           None        (64, 1)     add\n", "137              cart_photo_exposure_channel_fake0/w:0           None      (200, 64)     add\n", "138              cart_photo_exposure_channel_fake1/b:0           None        (32, 1)     add\n", "139              cart_photo_exposure_channel_fake1/w:0           None       (64, 32)     add\n", "140               cart_photo_exposure_dnn_layer/beta:0           None      (3000, 1)     add\n", "141              cart_photo_exposure_dnn_layer/gamma:0           None      (3000, 1)     add\n", "142             cart_photo_exposure_duration_fake0/b:0           None        (64, 1)     add\n", "143             cart_photo_exposure_duration_fake0/w:0           None      (200, 64)     add\n", "144             cart_photo_exposure_duration_fake1/b:0           None        (32, 1)     add\n", "145             cart_photo_exposure_duration_fake1/w:0           None       (64, 32)     add\n", "146   cart_photo_exposure_layer_new_0/LayerNorm/beta:0           None       (128, 1)     add\n", "147  cart_photo_exposure_layer_new_0/LayerNorm/gamma:0           None       (128, 1)     add\n", "148                cart_photo_exposure_layer_new_0/b:0           None       (128, 1)     add\n", "149                cart_photo_exposure_layer_new_0/w:0           None      (60, 128)     add\n", "150   cart_photo_exposure_layer_new_1/LayerNorm/beta:0           None        (64, 1)     add\n", "151  cart_photo_exposure_layer_new_1/LayerNorm/gamma:0           None        (64, 1)     add\n", "152                cart_photo_exposure_layer_new_1/b:0           None        (64, 1)     add\n", "153                cart_photo_exposure_layer_new_1/w:0           None      (128, 64)     add\n", "154                cart_photo_exposure_layer_new_2/b:0           None        (32, 1)     add\n", "155                cart_photo_exposure_layer_new_2/w:0           None       (64, 32)     add\n", "156                  cart_photo_exposure_pid_fake0/b:0           None        (64, 1)     add\n", "157                  cart_photo_exposure_pid_fake0/w:0           None      (800, 64)     add\n", "158                  cart_photo_exposure_pid_fake1/b:0           None        (32, 1)     add\n", "159                  cart_photo_exposure_pid_fake1/w:0           None       (64, 32)     add\n", "160            cart_photo_exposure_play_time_fake0/b:0           None        (64, 1)     add\n", "161            cart_photo_exposure_play_time_fake0/w:0           None      (200, 64)     add\n", "162            cart_photo_exposure_play_time_fake1/b:0           None        (32, 1)     add\n", "163            cart_photo_exposure_play_time_fake1/w:0           None       (64, 32)     add\n", "164               cart_photo_exposure_spu_id_fake0/b:0           None        (64, 1)     add\n", "165               cart_photo_exposure_spu_id_fake0/w:0           None      (800, 64)     add\n", "166               cart_photo_exposure_spu_id_fake1/b:0           None        (32, 1)     add\n", "167               cart_photo_exposure_spu_id_fake1/w:0           None       (64, 32)     add\n", "168     good_click_cate2cate_attentionk_trans_matrix:0           None       (84, 32)     add\n", "169     good_click_cate2cate_attentionq_trans_matrix:0           None      (288, 32)     add\n", "170     good_click_cate2cate_attentionv_trans_matrix:0           None       (84, 32)     add\n", "171               good_click_cate2cate_cate1_fake0/b:0           None        (64, 1)     add\n", "172               good_click_cate2cate_cate1_fake0/w:0           None      (400, 64)     add\n", "173               good_click_cate2cate_cate1_fake1/b:0           None        (32, 1)     add\n", "174               good_click_cate2cate_cate1_fake1/w:0           None       (64, 32)     add\n", "175          good_click_cate2cate_click_type_fake0/b:0           None        (64, 1)     add\n", "176          good_click_cate2cate_click_type_fake0/w:0           None      (200, 64)     add\n", "177          good_click_cate2cate_click_type_fake1/b:0           None        (32, 1)     add\n", "178          good_click_cate2cate_click_type_fake1/w:0           None       (64, 32)     add\n", "179               good_click_cate2cate_index_fake0/b:0           None        (64, 1)     add\n", "180               good_click_cate2cate_index_fake0/w:0           None      (200, 64)     add\n", "181               good_click_cate2cate_index_fake1/b:0           None        (32, 1)     add\n", "182               good_click_cate2cate_index_fake1/w:0           None       (64, 32)     add\n", "183            good_click_cate2cate_lag_hour_fake0/b:0           None        (64, 1)     add\n", "184            good_click_cate2cate_lag_hour_fake0/w:0           None      (400, 64)     add\n", "185            good_click_cate2cate_lag_hour_fake1/b:0           None        (32, 1)     add\n", "186            good_click_cate2cate_lag_hour_fake1/w:0           None       (64, 32)     add\n", "187                good_click_order_cate3_list_cl0/b:0           None        (64, 1)     add\n", "188                good_click_order_cate3_list_cl0/w:0           None      (400, 64)     add\n", "189                good_click_order_cate3_list_cl1/b:0           None        (32, 1)     add\n", "190                good_click_order_cate3_list_cl1/w:0           None       (64, 32)     add\n", "191                        good_click_order_dnn/beta:0           None      (6000, 1)     add\n", "192                       good_click_order_dnn/gamma:0           None      (6000, 1)     add\n", "193  good_click_order_embedding_attentionk_trans_ma...           None       (60, 32)     add\n", "194  good_click_order_embedding_attentionq_trans_ma...           None      (288, 32)     add\n", "195  good_click_order_embedding_attentionv_trans_ma...           None       (60, 32)     add\n", "196  good_click_order_embedding_sumpooling_pooling_...           None       (256, 1)     add\n", "197  good_click_order_embedding_sumpooling_pooling_...           None       (256, 1)     add\n", "198  good_click_order_embedding_sumpooling_pooling_...           None       (256, 1)     add\n", "199  good_click_order_embedding_sumpooling_pooling_...           None     (348, 256)     add\n", "200  good_click_order_embedding_sumpooling_pooling_...           None        (32, 1)     add\n", "201  good_click_order_embedding_sumpooling_pooling_...           None      (256, 32)     add\n", "202              good_click_order_item_id_list_cl0/b:0           None        (64, 1)     add\n", "203              good_click_order_item_id_list_cl0/w:0           None      (800, 64)     add\n", "204              good_click_order_item_id_list_cl1/b:0           None        (32, 1)     add\n", "205              good_click_order_item_id_list_cl1/w:0           None       (64, 32)     add\n", "206                good_click_order_label_list_cl0/b:0           None        (64, 1)     add\n", "207                good_click_order_label_list_cl0/w:0           None      (200, 64)     add\n", "208                good_click_order_label_list_cl1/b:0           None        (32, 1)     add\n", "209                good_click_order_label_list_cl1/w:0           None       (64, 32)     add\n", "210             good_click_order_lag_hour_list_cl0/b:0           None        (64, 1)     add\n", "211             good_click_order_lag_hour_list_cl0/w:0           None      (400, 64)     add\n", "212             good_click_order_lag_hour_list_cl1/b:0           None        (32, 1)     add\n", "213             good_click_order_lag_hour_list_cl1/w:0           None       (64, 32)     add\n", "214       good_click_order_page_view_time_list_cl0/b:0           None        (64, 1)     add\n", "215       good_click_order_page_view_time_list_cl0/w:0           None      (200, 64)     add\n", "216       good_click_order_page_view_time_list_cl1/b:0           None        (32, 1)     add\n", "217       good_click_order_page_view_time_list_cl1/w:0           None       (64, 32)     add\n", "218           good_click_order_price_diff_list_cl0/b:0           None        (64, 1)     add\n", "219           good_click_order_price_diff_list_cl0/w:0           None      (200, 64)     add\n", "220           good_click_order_price_diff_list_cl1/b:0           None        (32, 1)     add\n", "221           good_click_order_price_diff_list_cl1/w:0           None       (64, 32)     add\n", "222            good_click_order_seller_id_list_cl0/b:0           None        (64, 1)     add\n", "223            good_click_order_seller_id_list_cl0/w:0           None      (800, 64)     add\n", "224            good_click_order_seller_id_list_cl1/b:0           None        (32, 1)     add\n", "225            good_click_order_seller_id_list_cl1/w:0           None       (64, 32)     add\n", "226    good_click_softsearch_attentionk_trans_matrix:0           None       (72, 32)     add\n", "227    good_click_softsearch_attentionq_trans_matrix:0           None      (288, 32)     add\n", "228    good_click_softsearch_attentionv_trans_matrix:0           None       (72, 32)     add\n", "229         good_click_softsearch_carry_type_fake0/b:0           None        (64, 1)     add\n", "230         good_click_softsearch_carry_type_fake0/w:0           None      (200, 64)     add\n", "231         good_click_softsearch_carry_type_fake1/b:0           None        (32, 1)     add\n", "232         good_click_softsearch_carry_type_fake1/w:0           None       (64, 32)     add\n", "233           good_click_softsearch_category_fake0/b:0           None        (64, 1)     add\n", "234           good_click_softsearch_category_fake0/w:0           None      (400, 64)     add\n", "235           good_click_softsearch_category_fake1/b:0           None        (32, 1)     add\n", "236           good_click_softsearch_category_fake1/w:0           None       (64, 32)     add\n", "237         good_click_softsearch_item_count_fake0/b:0           None        (64, 1)     add\n", "238         good_click_softsearch_item_count_fake0/w:0           None      (200, 64)     add\n", "239         good_click_softsearch_item_count_fake1/b:0           None        (32, 1)     add\n", "240         good_click_softsearch_item_count_fake1/w:0           None       (64, 32)     add\n", "241            good_click_softsearch_item_id_fake0/b:0           None        (64, 1)     add\n", "242            good_click_softsearch_item_id_fake0/w:0           None      (800, 64)     add\n", "243            good_click_softsearch_item_id_fake1/b:0           None        (32, 1)     add\n", "244            good_click_softsearch_item_id_fake1/w:0           None       (64, 32)     add\n", "245                good_click_softsearch_lag_fake0/b:0           None        (64, 1)     add\n", "246                good_click_softsearch_lag_fake0/w:0           None      (400, 64)     add\n", "247                good_click_softsearch_lag_fake1/b:0           None        (32, 1)     add\n", "248                good_click_softsearch_lag_fake1/w:0           None       (64, 32)     add\n", "249  good_click_softsearch_layer_new_0/LayerNorm/be...           None       (128, 1)     add\n", "250  good_click_softsearch_layer_new_0/LayerNorm/ga...           None       (128, 1)     add\n", "251              good_click_softsearch_layer_new_0/b:0           None       (128, 1)     add\n", "252              good_click_softsearch_layer_new_0/w:0           None      (72, 128)     add\n", "253  good_click_softsearch_layer_new_1/LayerNorm/be...           None        (64, 1)     add\n", "254  good_click_softsearch_layer_new_1/LayerNorm/ga...           None        (64, 1)     add\n", "255              good_click_softsearch_layer_new_1/b:0           None        (64, 1)     add\n", "256              good_click_softsearch_layer_new_1/w:0           None      (128, 64)     add\n", "257              good_click_softsearch_layer_new_2/b:0           None        (32, 1)     add\n", "258              good_click_softsearch_layer_new_2/w:0           None       (64, 32)     add\n", "259              good_click_softsearch_price_fake0/b:0           None        (64, 1)     add\n", "260              good_click_softsearch_price_fake0/w:0           None      (400, 64)     add\n", "261              good_click_softsearch_price_fake1/b:0           None        (32, 1)     add\n", "262              good_click_softsearch_price_fake1/w:0           None       (64, 32)     add\n", "263          good_click_softsearch_seller_id_fake0/b:0           None        (64, 1)     add\n", "264          good_click_softsearch_seller_id_fake0/w:0           None      (800, 64)     add\n", "265          good_click_softsearch_seller_id_fake1/b:0           None        (32, 1)     add\n", "266          good_click_softsearch_seller_id_fake1/w:0           None       (64, 32)     add\n", "267       good_click_softsearch_topk_indices_fake0/b:0           None        (64, 1)     add\n", "268       good_click_softsearch_topk_indices_fake0/w:0           None      (200, 64)     add\n", "269       good_click_softsearch_topk_indices_fake1/b:0           None        (32, 1)     add\n", "270       good_click_softsearch_topk_indices_fake1/w:0           None       (64, 32)     add\n", "271        good_click_softsearch_topk_values_fake0/b:0           None        (64, 1)     add\n", "272        good_click_softsearch_topk_values_fake0/w:0           None      (200, 64)     add\n", "273        good_click_softsearch_topk_values_fake1/b:0           None        (32, 1)     add\n", "274        good_click_softsearch_topk_values_fake1/w:0           None       (64, 32)     add\n", "275        good_click_softsearch_user_embedding/beta:0           None      (3600, 1)     add\n", "276       good_click_softsearch_user_embedding/gamma:0           None      (3600, 1)     add\n", "277  good_show_user_embedding_attentionk_trans_matr...           None       (48, 32)     add\n", "278  good_show_user_embedding_attentionq_trans_matr...           None      (288, 32)     add\n", "279  good_show_user_embedding_attentionv_trans_matr...           None       (48, 32)     add\n", "280  good_show_user_embedding_pooling_0/LayerNorm/b...           None       (256, 1)     add\n", "281  good_show_user_embedding_pooling_0/LayerNorm/g...           None       (256, 1)     add\n", "282             good_show_user_embedding_pooling_0/b:0           None       (256, 1)     add\n", "283             good_show_user_embedding_pooling_0/w:0           None     (336, 256)     add\n", "284             good_show_user_embedding_pooling_1/b:0           None        (32, 1)     add\n", "285             good_show_user_embedding_pooling_1/w:0           None      (256, 32)     add\n", "286         long_seq_suffix_pooling_0/LayerNorm/beta:0           None       (256, 1)     add\n", "287        long_seq_suffix_pooling_0/LayerNorm/gamma:0           None       (256, 1)     add\n", "288                      long_seq_suffix_pooling_0/b:0           None       (256, 1)     add\n", "289                      long_seq_suffix_pooling_0/w:0           None    (1080, 256)     add\n", "290                      long_seq_suffix_pooling_1/b:0           None        (32, 1)     add\n", "291                      long_seq_suffix_pooling_1/w:0           None      (256, 32)     add\n", "Dense Extra Diff: \n", "                                                  name origin_shape current_shape    type\n", "0                  QUEUE_SOFT/share_bottom_layer_0/w:0   (8008704,)    (8205312,)  change\n", "1                good_click_cate2cate_dnn_layer/beta:0      (3000,)       (4200,)  change\n", "2               good_click_cate2cate_dnn_layer/gamma:0      (3000,)       (4200,)  change\n", "3                 good_click_cate2cate_layer_new_0/w:0      (7680,)      (10752,)  change\n", "4                         share_bottom_layer_new_0/w:0  (14167040,)   (14363648,)  change\n", "5    attention_layer/good_show_user_embedding_atten...      (1536,)          None  delete\n", "6    attention_layer/good_show_user_embedding_atten...      (9216,)          None  delete\n", "7    attention_layer/good_show_user_embedding_atten...      (1536,)          None  delete\n", "8    attention_layer/good_show_user_embedding_pooli...       (256,)          None  delete\n", "9    attention_layer/good_show_user_embedding_pooli...       (256,)          None  delete\n", "10   attention_layer/good_show_user_embedding_pooli...       (256,)          None  delete\n", "11   attention_layer/good_show_user_embedding_pooli...     (86016,)          None  delete\n", "12   attention_layer/good_show_user_embedding_pooli...        (32,)          None  delete\n", "13   attention_layer/good_show_user_embedding_pooli...      (8192,)          None  delete\n", "14   attention_layer/long_seq_suffix_pooling_0/Laye...       (256,)          None  delete\n", "15   attention_layer/long_seq_suffix_pooling_0/Laye...       (256,)          None  delete\n", "16       attention_layer/long_seq_suffix_pooling_0/b:0       (256,)          None  delete\n", "17       attention_layer/long_seq_suffix_pooling_0/w:0    (276480,)          None  delete\n", "18       attention_layer/long_seq_suffix_pooling_1/b:0        (32,)          None  delete\n", "19       attention_layer/long_seq_suffix_pooling_1/w:0      (8192,)          None  delete\n", "20                                 LayerNorm_35/beta:0         None         (32,)     add\n", "21                                LayerNorm_35/gamma:0         None         (32,)     add\n", "22                                 LayerNorm_36/beta:0         None         (32,)     add\n", "23                                LayerNorm_36/gamma:0         None         (32,)     add\n", "24                                 LayerNorm_37/beta:0         None         (32,)     add\n", "25                                LayerNorm_37/gamma:0         None         (32,)     add\n", "26                                 LayerNorm_38/beta:0         None         (32,)     add\n", "27                                LayerNorm_38/gamma:0         None         (32,)     add\n", "28                                 LayerNorm_39/beta:0         None         (32,)     add\n", "29                                LayerNorm_39/gamma:0         None         (32,)     add\n", "30                                 LayerNorm_40/beta:0         None         (32,)     add\n", "31                                LayerNorm_40/gamma:0         None         (32,)     add\n", "32                                 LayerNorm_41/beta:0         None         (32,)     add\n", "33                                LayerNorm_41/gamma:0         None         (32,)     add\n", "34                                 LayerNorm_42/beta:0         None         (32,)     add\n", "35                                LayerNorm_42/gamma:0         None         (32,)     add\n", "36                                 LayerNorm_43/beta:0         None         (32,)     add\n", "37                                LayerNorm_43/gamma:0         None         (32,)     add\n", "38                                 LayerNorm_44/beta:0         None         (32,)     add\n", "39                                LayerNorm_44/gamma:0         None         (32,)     add\n", "40                                 LayerNorm_45/beta:0         None         (32,)     add\n", "41                                LayerNorm_45/gamma:0         None         (32,)     add\n", "42                                 LayerNorm_46/beta:0         None         (32,)     add\n", "43                                LayerNorm_46/gamma:0         None         (32,)     add\n", "44                                 LayerNorm_47/beta:0         None         (32,)     add\n", "45                                LayerNorm_47/gamma:0         None         (32,)     add\n", "46                                 LayerNorm_48/beta:0         None         (32,)     add\n", "47                                LayerNorm_48/gamma:0         None         (32,)     add\n", "48                                 LayerNorm_49/beta:0         None         (32,)     add\n", "49                                LayerNorm_49/gamma:0         None         (32,)     add\n", "50                                 LayerNorm_50/beta:0         None         (32,)     add\n", "51                                LayerNorm_50/gamma:0         None         (32,)     add\n", "52                                 LayerNorm_51/beta:0         None         (32,)     add\n", "53                                LayerNorm_51/gamma:0         None         (32,)     add\n", "54                                 LayerNorm_52/beta:0         None         (32,)     add\n", "55                                LayerNorm_52/gamma:0         None         (32,)     add\n", "56                                 LayerNorm_53/beta:0         None         (32,)     add\n", "57                                LayerNorm_53/gamma:0         None         (32,)     add\n", "58                                 LayerNorm_54/beta:0         None         (32,)     add\n", "59                                LayerNorm_54/gamma:0         None         (32,)     add\n", "60                                 LayerNorm_55/beta:0         None         (32,)     add\n", "61                                LayerNorm_55/gamma:0         None         (32,)     add\n", "62                                 LayerNorm_56/beta:0         None         (32,)     add\n", "63                                LayerNorm_56/gamma:0         None         (32,)     add\n", "64                                 LayerNorm_57/beta:0         None         (32,)     add\n", "65                                LayerNorm_57/gamma:0         None         (32,)     add\n", "66                                 LayerNorm_58/beta:0         None         (32,)     add\n", "67                                LayerNorm_58/gamma:0         None         (32,)     add\n", "68                                 LayerNorm_59/beta:0         None         (32,)     add\n", "69                                LayerNorm_59/gamma:0         None         (32,)     add\n", "70                                 LayerNorm_60/beta:0         None         (32,)     add\n", "71                                LayerNorm_60/gamma:0         None         (32,)     add\n", "72                                 LayerNorm_61/beta:0         None         (32,)     add\n", "73                                LayerNorm_61/gamma:0         None         (32,)     add\n", "74                                 LayerNorm_62/beta:0         None         (32,)     add\n", "75                                LayerNorm_62/gamma:0         None         (32,)     add\n", "76                                 LayerNorm_63/beta:0         None         (32,)     add\n", "77                                LayerNorm_63/gamma:0         None         (32,)     add\n", "78                                 LayerNorm_64/beta:0         None         (32,)     add\n", "79                                LayerNorm_64/gamma:0         None         (32,)     add\n", "80                                 LayerNorm_65/beta:0         None         (32,)     add\n", "81                                LayerNorm_65/gamma:0         None         (32,)     add\n", "82                                 LayerNorm_66/beta:0         None         (32,)     add\n", "83                                LayerNorm_66/gamma:0         None         (32,)     add\n", "84                                 LayerNorm_67/beta:0         None         (32,)     add\n", "85                                LayerNorm_67/gamma:0         None         (32,)     add\n", "86                                 LayerNorm_68/beta:0         None         (32,)     add\n", "87                                LayerNorm_68/gamma:0         None         (32,)     add\n", "88                                 LayerNorm_69/beta:0         None         (32,)     add\n", "89                                LayerNorm_69/gamma:0         None         (32,)     add\n", "90                                 LayerNorm_70/beta:0         None         (32,)     add\n", "91                                LayerNorm_70/gamma:0         None         (32,)     add\n", "92                                 LayerNorm_71/beta:0         None         (32,)     add\n", "93                                LayerNorm_71/gamma:0         None         (32,)     add\n", "94                                 LayerNorm_72/beta:0         None         (32,)     add\n", "95                                LayerNorm_72/gamma:0         None         (32,)     add\n", "96                                 LayerNorm_73/beta:0         None         (32,)     add\n", "97                                LayerNorm_73/gamma:0         None         (32,)     add\n", "98                                 LayerNorm_74/beta:0         None         (32,)     add\n", "99                                LayerNorm_74/gamma:0         None         (32,)     add\n", "100                                LayerNorm_75/beta:0         None         (32,)     add\n", "101                               LayerNorm_75/gamma:0         None         (32,)     add\n", "102                                LayerNorm_76/beta:0         None         (32,)     add\n", "103                               LayerNorm_76/gamma:0         None         (32,)     add\n", "104                                LayerNorm_77/beta:0         None         (32,)     add\n", "105                               LayerNorm_77/gamma:0         None         (32,)     add\n", "106                                LayerNorm_78/beta:0         None         (32,)     add\n", "107                               LayerNorm_78/gamma:0         None         (32,)     add\n", "108                                LayerNorm_79/beta:0         None         (32,)     add\n", "109                               LayerNorm_79/gamma:0         None         (32,)     add\n", "110                                LayerNorm_80/beta:0         None         (32,)     add\n", "111                               LayerNorm_80/gamma:0         None         (32,)     add\n", "112                                LayerNorm_81/beta:0         None         (32,)     add\n", "113                               LayerNorm_81/gamma:0         None         (32,)     add\n", "114                                LayerNorm_82/beta:0         None         (32,)     add\n", "115                               LayerNorm_82/gamma:0         None         (32,)     add\n", "116                                LayerNorm_83/beta:0         None         (32,)     add\n", "117                               LayerNorm_83/gamma:0         None         (32,)     add\n", "118                                LayerNorm_84/beta:0         None         (32,)     add\n", "119                               LayerNorm_84/gamma:0         None         (32,)     add\n", "120                                LayerNorm_85/beta:0         None         (32,)     add\n", "121                               LayerNorm_85/gamma:0         None         (32,)     add\n", "122                                LayerNorm_86/beta:0         None         (32,)     add\n", "123                               LayerNorm_86/gamma:0         None         (32,)     add\n", "124                                LayerNorm_87/beta:0         None         (32,)     add\n", "125                               LayerNorm_87/gamma:0         None         (32,)     add\n", "126                                LayerNorm_88/beta:0         None         (32,)     add\n", "127                               LayerNorm_88/gamma:0         None         (32,)     add\n", "128                  cart_photo_exposure_aid_fake0/b:0         None         (64,)     add\n", "129                  cart_photo_exposure_aid_fake0/w:0         None      (25600,)     add\n", "130                  cart_photo_exposure_aid_fake1/b:0         None         (32,)     add\n", "131                  cart_photo_exposure_aid_fake1/w:0         None       (2048,)     add\n", "132             cart_photo_exposure_category_fake0/b:0         None         (64,)     add\n", "133             cart_photo_exposure_category_fake0/w:0         None      (25600,)     add\n", "134             cart_photo_exposure_category_fake1/b:0         None         (32,)     add\n", "135             cart_photo_exposure_category_fake1/w:0         None       (2048,)     add\n", "136              cart_photo_exposure_channel_fake0/b:0         None         (64,)     add\n", "137              cart_photo_exposure_channel_fake0/w:0         None      (12800,)     add\n", "138              cart_photo_exposure_channel_fake1/b:0         None         (32,)     add\n", "139              cart_photo_exposure_channel_fake1/w:0         None       (2048,)     add\n", "140               cart_photo_exposure_dnn_layer/beta:0         None       (3000,)     add\n", "141              cart_photo_exposure_dnn_layer/gamma:0         None       (3000,)     add\n", "142             cart_photo_exposure_duration_fake0/b:0         None         (64,)     add\n", "143             cart_photo_exposure_duration_fake0/w:0         None      (12800,)     add\n", "144             cart_photo_exposure_duration_fake1/b:0         None         (32,)     add\n", "145             cart_photo_exposure_duration_fake1/w:0         None       (2048,)     add\n", "146   cart_photo_exposure_layer_new_0/LayerNorm/beta:0         None        (128,)     add\n", "147  cart_photo_exposure_layer_new_0/LayerNorm/gamma:0         None        (128,)     add\n", "148                cart_photo_exposure_layer_new_0/b:0         None        (128,)     add\n", "149                cart_photo_exposure_layer_new_0/w:0         None       (7680,)     add\n", "150   cart_photo_exposure_layer_new_1/LayerNorm/beta:0         None         (64,)     add\n", "151  cart_photo_exposure_layer_new_1/LayerNorm/gamma:0         None         (64,)     add\n", "152                cart_photo_exposure_layer_new_1/b:0         None         (64,)     add\n", "153                cart_photo_exposure_layer_new_1/w:0         None       (8192,)     add\n", "154                cart_photo_exposure_layer_new_2/b:0         None         (32,)     add\n", "155                cart_photo_exposure_layer_new_2/w:0         None       (2048,)     add\n", "156                  cart_photo_exposure_pid_fake0/b:0         None         (64,)     add\n", "157                  cart_photo_exposure_pid_fake0/w:0         None      (51200,)     add\n", "158                  cart_photo_exposure_pid_fake1/b:0         None         (32,)     add\n", "159                  cart_photo_exposure_pid_fake1/w:0         None       (2048,)     add\n", "160            cart_photo_exposure_play_time_fake0/b:0         None         (64,)     add\n", "161            cart_photo_exposure_play_time_fake0/w:0         None      (12800,)     add\n", "162            cart_photo_exposure_play_time_fake1/b:0         None         (32,)     add\n", "163            cart_photo_exposure_play_time_fake1/w:0         None       (2048,)     add\n", "164               cart_photo_exposure_spu_id_fake0/b:0         None         (64,)     add\n", "165               cart_photo_exposure_spu_id_fake0/w:0         None      (51200,)     add\n", "166               cart_photo_exposure_spu_id_fake1/b:0         None         (32,)     add\n", "167               cart_photo_exposure_spu_id_fake1/w:0         None       (2048,)     add\n", "168     good_click_cate2cate_attentionk_trans_matrix:0         None       (2688,)     add\n", "169     good_click_cate2cate_attentionq_trans_matrix:0         None       (9216,)     add\n", "170     good_click_cate2cate_attentionv_trans_matrix:0         None       (2688,)     add\n", "171               good_click_cate2cate_cate1_fake0/b:0         None         (64,)     add\n", "172               good_click_cate2cate_cate1_fake0/w:0         None      (25600,)     add\n", "173               good_click_cate2cate_cate1_fake1/b:0         None         (32,)     add\n", "174               good_click_cate2cate_cate1_fake1/w:0         None       (2048,)     add\n", "175          good_click_cate2cate_click_type_fake0/b:0         None         (64,)     add\n", "176          good_click_cate2cate_click_type_fake0/w:0         None      (12800,)     add\n", "177          good_click_cate2cate_click_type_fake1/b:0         None         (32,)     add\n", "178          good_click_cate2cate_click_type_fake1/w:0         None       (2048,)     add\n", "179               good_click_cate2cate_index_fake0/b:0         None         (64,)     add\n", "180               good_click_cate2cate_index_fake0/w:0         None      (12800,)     add\n", "181               good_click_cate2cate_index_fake1/b:0         None         (32,)     add\n", "182               good_click_cate2cate_index_fake1/w:0         None       (2048,)     add\n", "183            good_click_cate2cate_lag_hour_fake0/b:0         None         (64,)     add\n", "184            good_click_cate2cate_lag_hour_fake0/w:0         None      (25600,)     add\n", "185            good_click_cate2cate_lag_hour_fake1/b:0         None         (32,)     add\n", "186            good_click_cate2cate_lag_hour_fake1/w:0         None       (2048,)     add\n", "187                good_click_order_cate3_list_cl0/b:0         None         (64,)     add\n", "188                good_click_order_cate3_list_cl0/w:0         None      (25600,)     add\n", "189                good_click_order_cate3_list_cl1/b:0         None         (32,)     add\n", "190                good_click_order_cate3_list_cl1/w:0         None       (2048,)     add\n", "191                        good_click_order_dnn/beta:0         None       (6000,)     add\n", "192                       good_click_order_dnn/gamma:0         None       (6000,)     add\n", "193  good_click_order_embedding_attentionk_trans_ma...         None       (1920,)     add\n", "194  good_click_order_embedding_attentionq_trans_ma...         None       (9216,)     add\n", "195  good_click_order_embedding_attentionv_trans_ma...         None       (1920,)     add\n", "196  good_click_order_embedding_sumpooling_pooling_...         None        (256,)     add\n", "197  good_click_order_embedding_sumpooling_pooling_...         None        (256,)     add\n", "198  good_click_order_embedding_sumpooling_pooling_...         None        (256,)     add\n", "199  good_click_order_embedding_sumpooling_pooling_...         None      (89088,)     add\n", "200  good_click_order_embedding_sumpooling_pooling_...         None         (32,)     add\n", "201  good_click_order_embedding_sumpooling_pooling_...         None       (8192,)     add\n", "202              good_click_order_item_id_list_cl0/b:0         None         (64,)     add\n", "203              good_click_order_item_id_list_cl0/w:0         None      (51200,)     add\n", "204              good_click_order_item_id_list_cl1/b:0         None         (32,)     add\n", "205              good_click_order_item_id_list_cl1/w:0         None       (2048,)     add\n", "206                good_click_order_label_list_cl0/b:0         None         (64,)     add\n", "207                good_click_order_label_list_cl0/w:0         None      (12800,)     add\n", "208                good_click_order_label_list_cl1/b:0         None         (32,)     add\n", "209                good_click_order_label_list_cl1/w:0         None       (2048,)     add\n", "210             good_click_order_lag_hour_list_cl0/b:0         None         (64,)     add\n", "211             good_click_order_lag_hour_list_cl0/w:0         None      (25600,)     add\n", "212             good_click_order_lag_hour_list_cl1/b:0         None         (32,)     add\n", "213             good_click_order_lag_hour_list_cl1/w:0         None       (2048,)     add\n", "214       good_click_order_page_view_time_list_cl0/b:0         None         (64,)     add\n", "215       good_click_order_page_view_time_list_cl0/w:0         None      (12800,)     add\n", "216       good_click_order_page_view_time_list_cl1/b:0         None         (32,)     add\n", "217       good_click_order_page_view_time_list_cl1/w:0         None       (2048,)     add\n", "218           good_click_order_price_diff_list_cl0/b:0         None         (64,)     add\n", "219           good_click_order_price_diff_list_cl0/w:0         None      (12800,)     add\n", "220           good_click_order_price_diff_list_cl1/b:0         None         (32,)     add\n", "221           good_click_order_price_diff_list_cl1/w:0         None       (2048,)     add\n", "222            good_click_order_seller_id_list_cl0/b:0         None         (64,)     add\n", "223            good_click_order_seller_id_list_cl0/w:0         None      (51200,)     add\n", "224            good_click_order_seller_id_list_cl1/b:0         None         (32,)     add\n", "225            good_click_order_seller_id_list_cl1/w:0         None       (2048,)     add\n", "226    good_click_softsearch_attentionk_trans_matrix:0         None       (2304,)     add\n", "227    good_click_softsearch_attentionq_trans_matrix:0         None       (9216,)     add\n", "228    good_click_softsearch_attentionv_trans_matrix:0         None       (2304,)     add\n", "229         good_click_softsearch_carry_type_fake0/b:0         None         (64,)     add\n", "230         good_click_softsearch_carry_type_fake0/w:0         None      (12800,)     add\n", "231         good_click_softsearch_carry_type_fake1/b:0         None         (32,)     add\n", "232         good_click_softsearch_carry_type_fake1/w:0         None       (2048,)     add\n", "233           good_click_softsearch_category_fake0/b:0         None         (64,)     add\n", "234           good_click_softsearch_category_fake0/w:0         None      (25600,)     add\n", "235           good_click_softsearch_category_fake1/b:0         None         (32,)     add\n", "236           good_click_softsearch_category_fake1/w:0         None       (2048,)     add\n", "237         good_click_softsearch_item_count_fake0/b:0         None         (64,)     add\n", "238         good_click_softsearch_item_count_fake0/w:0         None      (12800,)     add\n", "239         good_click_softsearch_item_count_fake1/b:0         None         (32,)     add\n", "240         good_click_softsearch_item_count_fake1/w:0         None       (2048,)     add\n", "241            good_click_softsearch_item_id_fake0/b:0         None         (64,)     add\n", "242            good_click_softsearch_item_id_fake0/w:0         None      (51200,)     add\n", "243            good_click_softsearch_item_id_fake1/b:0         None         (32,)     add\n", "244            good_click_softsearch_item_id_fake1/w:0         None       (2048,)     add\n", "245                good_click_softsearch_lag_fake0/b:0         None         (64,)     add\n", "246                good_click_softsearch_lag_fake0/w:0         None      (25600,)     add\n", "247                good_click_softsearch_lag_fake1/b:0         None         (32,)     add\n", "248                good_click_softsearch_lag_fake1/w:0         None       (2048,)     add\n", "249  good_click_softsearch_layer_new_0/LayerNorm/be...         None        (128,)     add\n", "250  good_click_softsearch_layer_new_0/LayerNorm/ga...         None        (128,)     add\n", "251              good_click_softsearch_layer_new_0/b:0         None        (128,)     add\n", "252              good_click_softsearch_layer_new_0/w:0         None       (9216,)     add\n", "253  good_click_softsearch_layer_new_1/LayerNorm/be...         None         (64,)     add\n", "254  good_click_softsearch_layer_new_1/LayerNorm/ga...         None         (64,)     add\n", "255              good_click_softsearch_layer_new_1/b:0         None         (64,)     add\n", "256              good_click_softsearch_layer_new_1/w:0         None       (8192,)     add\n", "257              good_click_softsearch_layer_new_2/b:0         None         (32,)     add\n", "258              good_click_softsearch_layer_new_2/w:0         None       (2048,)     add\n", "259              good_click_softsearch_price_fake0/b:0         None         (64,)     add\n", "260              good_click_softsearch_price_fake0/w:0         None      (25600,)     add\n", "261              good_click_softsearch_price_fake1/b:0         None         (32,)     add\n", "262              good_click_softsearch_price_fake1/w:0         None       (2048,)     add\n", "263          good_click_softsearch_seller_id_fake0/b:0         None         (64,)     add\n", "264          good_click_softsearch_seller_id_fake0/w:0         None      (51200,)     add\n", "265          good_click_softsearch_seller_id_fake1/b:0         None         (32,)     add\n", "266          good_click_softsearch_seller_id_fake1/w:0         None       (2048,)     add\n", "267       good_click_softsearch_topk_indices_fake0/b:0         None         (64,)     add\n", "268       good_click_softsearch_topk_indices_fake0/w:0         None      (12800,)     add\n", "269       good_click_softsearch_topk_indices_fake1/b:0         None         (32,)     add\n", "270       good_click_softsearch_topk_indices_fake1/w:0         None       (2048,)     add\n", "271        good_click_softsearch_topk_values_fake0/b:0         None         (64,)     add\n", "272        good_click_softsearch_topk_values_fake0/w:0         None      (12800,)     add\n", "273        good_click_softsearch_topk_values_fake1/b:0         None         (32,)     add\n", "274        good_click_softsearch_topk_values_fake1/w:0         None       (2048,)     add\n", "275        good_click_softsearch_user_embedding/beta:0         None       (3600,)     add\n", "276       good_click_softsearch_user_embedding/gamma:0         None       (3600,)     add\n", "277  good_show_user_embedding_attentionk_trans_matr...         None       (1536,)     add\n", "278  good_show_user_embedding_attentionq_trans_matr...         None       (9216,)     add\n", "279  good_show_user_embedding_attentionv_trans_matr...         None       (1536,)     add\n", "280  good_show_user_embedding_pooling_0/LayerNorm/b...         None        (256,)     add\n", "281  good_show_user_embedding_pooling_0/LayerNorm/g...         None        (256,)     add\n", "282             good_show_user_embedding_pooling_0/b:0         None        (256,)     add\n", "283             good_show_user_embedding_pooling_0/w:0         None      (86016,)     add\n", "284             good_show_user_embedding_pooling_1/b:0         None         (32,)     add\n", "285             good_show_user_embedding_pooling_1/w:0         None       (8192,)     add\n", "286         long_seq_suffix_pooling_0/LayerNorm/beta:0         None        (256,)     add\n", "287        long_seq_suffix_pooling_0/LayerNorm/gamma:0         None        (256,)     add\n", "288                      long_seq_suffix_pooling_0/b:0         None        (256,)     add\n", "289                      long_seq_suffix_pooling_0/w:0         None     (276480,)     add\n", "290                      long_seq_suffix_pooling_1/b:0         None         (32,)     add\n", "291                      long_seq_suffix_pooling_1/w:0         None       (8192,)     add\n"]}, {"data": {"text/plain": ["([{'name': 'QUEUE_SOFT/share_bottom_layer_0/w:0',\n", "   'origin_shape': (7821, 1024),\n", "   'current_shape': (8013, 1024),\n", "   'type': 'change'},\n", "  {'name': 'good_click_cate2cate_dnn_layer/beta:0',\n", "   'origin_shape': (3000, 1),\n", "   'current_shape': (4200, 1),\n", "   'type': 'change'},\n", "  {'name': 'good_click_cate2cate_dnn_layer/gamma:0',\n", "   'origin_shape': (3000, 1),\n", "   'current_shape': (4200, 1),\n", "   'type': 'change'},\n", "  {'name': 'good_click_cate2cate_layer_new_0/w:0',\n", "   'origin_shape': (60, 128),\n", "   'current_shape': (84, 128),\n", "   'type': 'change'},\n", "  {'name': 'share_bottom_layer_new_0/w:0',\n", "   'origin_shape': (13835, 1024),\n", "   'current_shape': (14027, 1024),\n", "   'type': 'change'},\n", "  {'name': 'attention_layer/good_show_user_embedding_attentionk_trans_matrix:0',\n", "   'origin_shape': (48, 32),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_attentionq_trans_matrix:0',\n", "   'origin_shape': (288, 32),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_attentionv_trans_matrix:0',\n", "   'origin_shape': (48, 32),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_0/LayerNorm/beta:0',\n", "   'origin_shape': (256, 1),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_0/LayerNorm/gamma:0',\n", "   'origin_shape': (256, 1),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_0/b:0',\n", "   'origin_shape': (256, 1),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_0/w:0',\n", "   'origin_shape': (336, 256),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_1/b:0',\n", "   'origin_shape': (32, 1),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_1/w:0',\n", "   'origin_shape': (256, 32),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_0/LayerNorm/beta:0',\n", "   'origin_shape': (256, 1),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_0/LayerNorm/gamma:0',\n", "   'origin_shape': (256, 1),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_0/b:0',\n", "   'origin_shape': (256, 1),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_0/w:0',\n", "   'origin_shape': (1080, 256),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_1/b:0',\n", "   'origin_shape': (32, 1),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_1/w:0',\n", "   'origin_shape': (256, 32),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'LayerNorm_35/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_35/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_36/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_36/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_37/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_37/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_38/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_38/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_39/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_39/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_40/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_40/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_41/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_41/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_42/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_42/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_43/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_43/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_44/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_44/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_45/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_45/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_46/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_46/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_47/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_47/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_48/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_48/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_49/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_49/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_50/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_50/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_51/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_51/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_52/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_52/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_53/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_53/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_54/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_54/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_55/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_55/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_56/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_56/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_57/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_57/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_58/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_58/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_59/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_59/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_60/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_60/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_61/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_61/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_62/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_62/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_63/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_63/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_64/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_64/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_65/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_65/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_66/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_66/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_67/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_67/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_68/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_68/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_69/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_69/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_70/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_70/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_71/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_71/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_72/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_72/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_73/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_73/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_74/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_74/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_75/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_75/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_76/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_76/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_77/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_77/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_78/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_78/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_79/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_79/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_80/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_80/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_81/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_81/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_82/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_82/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_83/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_83/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_84/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_84/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_85/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_85/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_86/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_86/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_87/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_87/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_88/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_88/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_aid_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_aid_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_aid_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_aid_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_category_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_category_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_category_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_category_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_channel_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_channel_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_channel_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_channel_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_dnn_layer/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (3000, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_dnn_layer/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (3000, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_duration_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_duration_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_duration_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_duration_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (60, 128),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_1/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_1/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 64),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_2/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_2/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_pid_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_pid_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (800, 64),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_pid_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_pid_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_play_time_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_play_time_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_play_time_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_play_time_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_spu_id_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_spu_id_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (800, 64),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_spu_id_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_spu_id_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_attentionk_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (84, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_attentionq_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (288, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_attentionv_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (84, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_cate1_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_cate1_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_cate1_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_cate1_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_click_type_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_click_type_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_click_type_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_click_type_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_index_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_index_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_index_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_index_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_lag_hour_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_lag_hour_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_lag_hour_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_lag_hour_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_cate3_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_cate3_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_cate3_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_cate3_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_dnn/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (6000, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_dnn/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (6000, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_attentionk_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (60, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_attentionq_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (288, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_attentionv_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (60, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (348, 256),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_item_id_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_item_id_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (800, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_item_id_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_item_id_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_label_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_label_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_label_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_label_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_lag_hour_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_lag_hour_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_lag_hour_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_lag_hour_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_page_view_time_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_page_view_time_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_page_view_time_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_page_view_time_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_price_diff_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_price_diff_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_price_diff_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_price_diff_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_seller_id_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_seller_id_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (800, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_seller_id_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_seller_id_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionk_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (72, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionq_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (288, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionv_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (72, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (800, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (72, 128),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_2/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_2/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (800, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (200, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_user_embedding/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (3600, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_user_embedding/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (3600, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_attentionk_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (48, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_attentionq_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (288, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_attentionv_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (48, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (336, 256),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 32),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (1080, 256),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 32),\n", "   'type': 'add'}],\n", " [{'name': 'QUEUE_SOFT/share_bottom_layer_0/w:0',\n", "   'origin_shape': (8008704,),\n", "   'current_shape': (8205312,),\n", "   'type': 'change'},\n", "  {'name': 'good_click_cate2cate_dnn_layer/beta:0',\n", "   'origin_shape': (3000,),\n", "   'current_shape': (4200,),\n", "   'type': 'change'},\n", "  {'name': 'good_click_cate2cate_dnn_layer/gamma:0',\n", "   'origin_shape': (3000,),\n", "   'current_shape': (4200,),\n", "   'type': 'change'},\n", "  {'name': 'good_click_cate2cate_layer_new_0/w:0',\n", "   'origin_shape': (7680,),\n", "   'current_shape': (10752,),\n", "   'type': 'change'},\n", "  {'name': 'share_bottom_layer_new_0/w:0',\n", "   'origin_shape': (14167040,),\n", "   'current_shape': (14363648,),\n", "   'type': 'change'},\n", "  {'name': 'attention_layer/good_show_user_embedding_attentionk_trans_matrix:0',\n", "   'origin_shape': (1536,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_attentionq_trans_matrix:0',\n", "   'origin_shape': (9216,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_attentionv_trans_matrix:0',\n", "   'origin_shape': (1536,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_0/LayerNorm/beta:0',\n", "   'origin_shape': (256,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_0/LayerNorm/gamma:0',\n", "   'origin_shape': (256,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_0/b:0',\n", "   'origin_shape': (256,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_0/w:0',\n", "   'origin_shape': (86016,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_1/b:0',\n", "   'origin_shape': (32,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/good_show_user_embedding_pooling_1/w:0',\n", "   'origin_shape': (8192,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_0/LayerNorm/beta:0',\n", "   'origin_shape': (256,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_0/LayerNorm/gamma:0',\n", "   'origin_shape': (256,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_0/b:0',\n", "   'origin_shape': (256,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_0/w:0',\n", "   'origin_shape': (276480,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_1/b:0',\n", "   'origin_shape': (32,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'attention_layer/long_seq_suffix_pooling_1/w:0',\n", "   'origin_shape': (8192,),\n", "   'current_shape': None,\n", "   'type': 'delete'},\n", "  {'name': 'LayerNorm_35/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_35/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_36/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_36/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_37/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_37/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_38/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_38/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_39/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_39/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_40/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_40/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_41/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_41/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_42/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_42/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_43/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_43/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_44/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_44/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_45/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_45/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_46/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_46/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_47/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_47/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_48/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_48/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_49/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_49/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_50/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_50/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_51/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_51/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_52/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_52/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_53/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_53/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_54/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_54/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_55/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_55/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_56/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_56/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_57/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_57/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_58/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_58/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_59/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_59/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_60/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_60/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_61/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_61/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_62/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_62/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_63/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_63/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_64/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_64/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_65/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_65/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_66/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_66/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_67/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_67/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_68/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_68/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_69/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_69/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_70/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_70/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_71/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_71/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_72/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_72/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_73/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_73/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_74/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_74/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_75/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_75/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_76/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_76/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_77/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_77/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_78/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_78/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_79/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_79/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_80/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_80/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_81/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_81/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_82/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_82/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_83/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_83/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_84/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_84/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_85/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_85/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_86/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_86/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_87/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_87/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_88/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_88/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_aid_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_aid_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_aid_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_aid_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_category_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_category_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_category_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_category_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_channel_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_channel_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_channel_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_channel_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_dnn_layer/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (3000,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_dnn_layer/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (3000,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_duration_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_duration_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_duration_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_duration_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (7680,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_1/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_1/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (8192,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_2/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_layer_new_2/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_pid_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_pid_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (51200,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_pid_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_pid_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_play_time_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_play_time_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_play_time_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_play_time_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_spu_id_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_spu_id_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (51200,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_spu_id_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'cart_photo_exposure_spu_id_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_attentionk_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2688,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_attentionq_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (9216,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_attentionv_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2688,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_cate1_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_cate1_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_cate1_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_cate1_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_click_type_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_click_type_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_click_type_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_click_type_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_index_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_index_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_index_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_index_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_lag_hour_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_lag_hour_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_lag_hour_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_cate2cate_lag_hour_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_cate3_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_cate3_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_cate3_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_cate3_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_dnn/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (6000,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_dnn/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (6000,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_attentionk_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (1920,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_attentionq_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (9216,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_attentionv_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (1920,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (89088,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_embedding_sumpooling_pooling_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (8192,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_item_id_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_item_id_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (51200,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_item_id_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_item_id_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_label_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_label_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_label_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_label_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_lag_hour_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_lag_hour_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_lag_hour_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_lag_hour_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_page_view_time_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_page_view_time_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_page_view_time_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_page_view_time_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_price_diff_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_price_diff_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_price_diff_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_price_diff_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_seller_id_list_cl0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_seller_id_list_cl0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (51200,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_seller_id_list_cl1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_order_seller_id_list_cl1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionk_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2304,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionq_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (9216,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionv_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2304,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (51200,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (9216,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (8192,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_2/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_2/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (51200,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (12800,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_user_embedding/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (3600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_user_embedding/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (3600,),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_attentionk_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (1536,),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_attentionq_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (9216,),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_attentionv_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (1536,),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (86016,),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_show_user_embedding_pooling_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (8192,),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (276480,),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'long_seq_suffix_pooling_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (8192,),\n", "   'type': 'add'}])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["old_yaml = 'suntianyu06_dsp_sty_ue_re_rocket.yaml'\n", "new_yaml = 'xiatian06_dsp_lps_sv_xt_good_click_soft_ue_v1.yaml'\n", "\n", "old_yaml = os.path.join(dirpath, old_yaml)\n", "new_yaml = os.path.join(dirpath, new_yaml)\n", "dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)\n", "# print(dense_old)\n", "dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)\n", "# print(dense_new)\n", "diff_dense_table(dense_old, dense_new)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = None\n", "    extra = None\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 第一层的权重的尺寸可能不一样，取决于输入\n", "    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。\n", "    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。\n", "    \n", "    # 这里是旧权重尺寸小(或无）的情况\n", "    for var_name in list(tf_weight): \n", "        if var_name == 'rocket_upper_layer_2/w:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:560, :] = ori_weight\n", "            new_extra[:560, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name == 'ue_score_sparse/beta:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:304, :] = ori_weight\n", "            new_extra[:304, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name == 'ue_score_sparse/gamma:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:304, :] = ori_weight\n", "            new_extra[:304, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name not in warmup_weight:\n", "            print(\"加载的 dense variable({}) 不存在，其值全新初始化\".format(var_name))\n", "            warmup_weight[var_name] = tf_weight[var_name]\n", "            warmup_extra[var_name] = ps_extra[var_name]\n", "\n", "            \n", "    if len(warmup_weight) > 0:\n", "        for var_name in list(warmup_weight):\n", "            if var_name not in tf_weight:\n", "                print(\"加载的 dense variable({}) 在运行时不存在，其值被忽略。\".format(var_name))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "#                     if var_name not in warmup_weight:\n", "#                         print(f\"{var_name} not in warmup_weight\")\n", "#                     else:\n", "#                         print(f\"{var_name} not in warmup_extra\")\n", "                    pass\n", "            elif warmup_weight[var_name].size != tf_weight[var_name].size:\n", "                # 这里可以添加旧权重尺寸大的处理逻辑\n", "                print(\"加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略\".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "                    if var_name not in warmup_weight:\n", "                        print(f\"{var_name} not in warmup_weight\")\n", "                    else:\n", "                        print(f\"{var_name} not in warmup_extra\")\n", "        weight = warmup_weight\n", "        extra = warmup_extra\n", "    else:\n", "        weight = tf_weight\n", "        extra = ps_extra\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["my_load_dense_func\n", "加载的 dense variable(cart_photo_exposure_dnn_layer/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_dnn_layer/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_dnn/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_dnn/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(ue_score_sparse/beta:0) 不存在，其值由ue_score_sparse/beta:0初始化, size is 304 and 304\n", "加载的 dense variable(ue_score_sparse/gamma:0) 不存在，其值由ue_score_sparse/gamma:0初始化, size is 304 and 304\n", "加载的 dense variable(good_click_softsearch_user_embedding/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_user_embedding/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_cate3_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_cate3_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_cate3_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_cate3_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_item_id_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_item_id_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_item_id_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_item_id_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_label_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_label_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_label_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_label_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_lag_hour_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_lag_hour_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_lag_hour_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_lag_hour_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_page_view_time_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_page_view_time_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_page_view_time_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_page_view_time_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_price_diff_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_price_diff_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_price_diff_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_price_diff_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_seller_id_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_seller_id_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_seller_id_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_seller_id_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_35/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_35/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_36/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_36/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_seq_suffix_pooling_0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_seq_suffix_pooling_0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_seq_suffix_pooling_0/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_seq_suffix_pooling_0/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_seq_suffix_pooling_1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(long_seq_suffix_pooling_1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_user_embedding_pooling_0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_user_embedding_pooling_0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_user_embedding_pooling_0/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_user_embedding_pooling_0/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_user_embedding_pooling_1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_user_embedding_pooling_1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_user_embedding_attentionq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_user_embedding_attentionk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_user_embedding_attentionv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_embedding_sumpooling_pooling_0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_embedding_sumpooling_pooling_0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_embedding_sumpooling_pooling_0/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_embedding_sumpooling_pooling_0/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_embedding_sumpooling_pooling_1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_embedding_sumpooling_pooling_1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_embedding_attentionq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_embedding_attentionk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_order_embedding_attentionv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_37/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_37/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_38/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_38/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_39/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_39/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_40/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_40/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_41/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_41/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_42/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_42/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_43/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_43/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_44/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_44/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_45/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_45/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_46/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_46/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_47/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_47/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_48/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_48/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_cate1_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_cate1_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_cate1_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_cate1_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_49/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_49/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_50/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_50/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_lag_hour_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_lag_hour_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_lag_hour_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_lag_hour_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_51/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_51/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_52/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_52/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_click_type_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_click_type_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_click_type_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_click_type_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_53/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_53/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_54/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_54/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_index_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_index_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_index_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_index_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_55/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_55/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_56/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_56/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_id_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_id_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_id_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_id_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_57/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_57/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_58/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_58/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_seller_id_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_seller_id_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_seller_id_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_seller_id_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_59/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_59/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_60/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_60/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_lag_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_lag_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_lag_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_lag_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_61/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_61/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_62/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_62/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_category_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_category_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_category_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_category_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_63/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_63/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_64/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_64/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_carry_type_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_carry_type_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_carry_type_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_carry_type_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_65/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_65/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_66/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_66/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_price_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_price_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_price_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_price_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_67/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_67/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_68/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_68/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_69/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_69/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_70/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_70/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_71/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_71/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_72/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_72/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_73/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_73/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_74/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_74/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_aid_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_aid_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_aid_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_aid_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_75/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_75/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_76/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_76/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_category_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_category_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_category_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_category_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_77/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_77/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_78/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_78/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_channel_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_channel_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_channel_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_channel_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_79/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_79/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_80/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_80/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_duration_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_duration_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_duration_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_duration_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_81/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_81/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_82/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_82/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_pid_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_pid_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_pid_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_pid_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_83/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_83/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_84/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_84/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_play_time_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_play_time_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_play_time_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_play_time_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_85/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_85/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_86/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_86/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_spu_id_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_spu_id_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_spu_id_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_spu_id_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_87/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_87/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_88/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_88/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_attentionq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_attentionk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_attentionv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_0/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_0/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_2/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_2/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_attentionq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_attentionk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_attentionv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_layer_new_0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_layer_new_0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_layer_new_0/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_layer_new_0/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_layer_new_1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_layer_new_1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_layer_new_1/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_layer_new_1/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_layer_new_2/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(cart_photo_exposure_layer_new_2/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(rocket_upper_layer_2/w:0) 不存在，其值由rocket_upper_layer_2/w:0初始化, size is 71680 and 71680\n", "加载的 dense variable(good_click_cate2cate_dnn_layer/beta:0) size (3000 vs 4200) 不匹配，其值被忽略\n", "加载的 dense variable(good_click_cate2cate_dnn_layer/gamma:0) size (3000 vs 4200) 不匹配，其值被忽略\n", "加载的 dense variable(attention_layer/long_seq_suffix_pooling_0/w:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/long_seq_suffix_pooling_0/b:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/long_seq_suffix_pooling_0/LayerNorm/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/long_seq_suffix_pooling_0/LayerNorm/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/long_seq_suffix_pooling_1/w:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/long_seq_suffix_pooling_1/b:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/good_show_user_embedding_pooling_0/w:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/good_show_user_embedding_pooling_0/b:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/good_show_user_embedding_pooling_0/LayerNorm/beta:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/good_show_user_embedding_pooling_0/LayerNorm/gamma:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/good_show_user_embedding_pooling_1/w:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/good_show_user_embedding_pooling_1/b:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/good_show_user_embedding_attentionq_trans_matrix:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/good_show_user_embedding_attentionk_trans_matrix:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(attention_layer/good_show_user_embedding_attentionv_trans_matrix:0) 在运行时不存在，其值被忽略。\n", "加载的 dense variable(good_click_cate2cate_layer_new_0/w:0) size (7680 vs 10752) 不匹配，其值被忽略\n", "加载的 dense variable(QUEUE_SOFT/share_bottom_layer_0/w:0) size (8008704 vs 8205312) 不匹配，其值被忽略\n", "加载的 dense variable(share_bottom_layer_new_0/w:0) size (14167040 vs 14363648) 不匹配，其值被忽略\n"]}, {"ename": "AssertionError", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m res_weight, res_extra \u001b[38;5;241m=\u001b[39m mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n\u001b[1;32m      2\u001b[0m dense_change \u001b[38;5;241m=\u001b[39m get_dense_table_from_dict(res_weight, res_extra)\n\u001b[1;32m      3\u001b[0m diff_dense_table(dense_new, dense_change)\n", "File \u001b[0;32m~/Documents/python/DNNFIX/pylib/dnn_fix_v2.py:308\u001b[0m, in \u001b[0;36mmock_load_dense_func\u001b[0;34m(load_dense_func, warmup_dense_table, current_dense_table, load_option)\u001b[0m\n\u001b[1;32m    305\u001b[0m load_option_obj \u001b[38;5;241m=\u001b[39m Dict2Obj(load_option)\n\u001b[1;32m    307\u001b[0m \u001b[38;5;66;03m# 调用 load_dense_func，模拟真实场景的行为，打印结果\u001b[39;00m\n\u001b[0;32m--> 308\u001b[0m weight, extra \u001b[38;5;241m=\u001b[39m load_dense_func(\n\u001b[1;32m    309\u001b[0m     warmup_weight, warmup_extra, ps_weight, ps_extra, tf_weight, load_option_obj)\n\u001b[1;32m    311\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mweight_res.txt\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mw+\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[1;32m    312\u001b[0m     f\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;28mstr\u001b[39m(weight))\n", "Cell \u001b[0;32mIn[3], line 104\u001b[0m, in \u001b[0;36mmy_load_dense_func\u001b[0;34m(warmup_weight, warmup_extra, ps_weight, ps_extra, tf_weight, load_option)\u001b[0m\n\u001b[1;32m    101\u001b[0m     weight \u001b[38;5;241m=\u001b[39m tf_weight\n\u001b[1;32m    102\u001b[0m     extra \u001b[38;5;241m=\u001b[39m ps_extra\n\u001b[0;32m--> 104\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(weight) \u001b[38;5;241m==\u001b[39m dense_variable_nums\n\u001b[1;32m    105\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(extra) \u001b[38;5;241m==\u001b[39m dense_variable_nums\n\u001b[1;32m    107\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mend my_load_dense_func\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[0;31mAssertionError\u001b[0m: "]}], "source": ["res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n", "dense_change = get_dense_table_from_dict(res_weight, res_extra)\n", "diff_dense_table(dense_new, dense_change)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}