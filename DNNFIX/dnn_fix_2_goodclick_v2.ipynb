{"cells": [{"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "dirpath = \"/Users/<USER>/Downloads\"\n", "sys.path.append(dirpath)\n", "from pylib.dnn_fix_v2 import *"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense Weight Diff: \n", "                                                 name   origin_shape  current_shape    type\n", "0                 QUEUE_SOFT/share_bottom_layer_0/w:0   (7885, 1024)   (7949, 1024)  change\n", "1      good_click_cate2cate_attentionq_trans_matrix:0      (288, 32)      (512, 32)  change\n", "2   good_show_user_embedding_attentionq_trans_matr...      (288, 32)      (512, 32)  change\n", "3              good_show_user_embedding_pooling_0/w:0     (336, 256)     (560, 256)  change\n", "4                             goods_feat_layer/beta:0       (288, 1)       (512, 1)  change\n", "5                            goods_feat_layer/gamma:0       (288, 1)       (512, 1)  change\n", "6                        share_bottom_layer_new_0/w:0  (13899, 1024)  (14187, 1024)  change\n", "7                                 LayerNorm_57/beta:0           None        (32, 1)     add\n", "8                                LayerNorm_57/gamma:0           None        (32, 1)     add\n", "9                                 LayerNorm_58/beta:0           None        (32, 1)     add\n", "10                               LayerNorm_58/gamma:0           None        (32, 1)     add\n", "11                                LayerNorm_59/beta:0           None        (32, 1)     add\n", "12                               LayerNorm_59/gamma:0           None        (32, 1)     add\n", "13                                LayerNorm_60/beta:0           None        (32, 1)     add\n", "14                               LayerNorm_60/gamma:0           None        (32, 1)     add\n", "15                                LayerNorm_61/beta:0           None        (32, 1)     add\n", "16                               LayerNorm_61/gamma:0           None        (32, 1)     add\n", "17                                LayerNorm_62/beta:0           None        (32, 1)     add\n", "18                               LayerNorm_62/gamma:0           None        (32, 1)     add\n", "19                                LayerNorm_63/beta:0           None        (32, 1)     add\n", "20                               LayerNorm_63/gamma:0           None        (32, 1)     add\n", "21                                LayerNorm_64/beta:0           None        (32, 1)     add\n", "22                               LayerNorm_64/gamma:0           None        (32, 1)     add\n", "23                                LayerNorm_65/beta:0           None        (32, 1)     add\n", "24                               LayerNorm_65/gamma:0           None        (32, 1)     add\n", "25                                LayerNorm_66/beta:0           None        (32, 1)     add\n", "26                               LayerNorm_66/gamma:0           None        (32, 1)     add\n", "27                                LayerNorm_67/beta:0           None        (32, 1)     add\n", "28                               LayerNorm_67/gamma:0           None        (32, 1)     add\n", "29                                LayerNorm_68/beta:0           None        (32, 1)     add\n", "30                               LayerNorm_68/gamma:0           None        (32, 1)     add\n", "31                                LayerNorm_69/beta:0           None        (32, 1)     add\n", "32                               LayerNorm_69/gamma:0           None        (32, 1)     add\n", "33                                LayerNorm_70/beta:0           None        (32, 1)     add\n", "34                               LayerNorm_70/gamma:0           None        (32, 1)     add\n", "35                                LayerNorm_71/beta:0           None        (32, 1)     add\n", "36                               LayerNorm_71/gamma:0           None        (32, 1)     add\n", "37                                LayerNorm_72/beta:0           None        (32, 1)     add\n", "38                               LayerNorm_72/gamma:0           None        (32, 1)     add\n", "39                                LayerNorm_73/beta:0           None        (32, 1)     add\n", "40                               LayerNorm_73/gamma:0           None        (32, 1)     add\n", "41                                LayerNorm_74/beta:0           None        (32, 1)     add\n", "42                               LayerNorm_74/gamma:0           None        (32, 1)     add\n", "43    good_click_softsearch_attentionk_trans_matrix:0           None       (72, 32)     add\n", "44    good_click_softsearch_attentionq_trans_matrix:0           None      (512, 32)     add\n", "45    good_click_softsearch_attentionv_trans_matrix:0           None       (72, 32)     add\n", "46         good_click_softsearch_carry_type_fake0/b:0           None        (64, 1)     add\n", "47         good_click_softsearch_carry_type_fake0/w:0           None      (400, 64)     add\n", "48         good_click_softsearch_carry_type_fake1/b:0           None        (32, 1)     add\n", "49         good_click_softsearch_carry_type_fake1/w:0           None       (64, 32)     add\n", "50           good_click_softsearch_category_fake0/b:0           None        (64, 1)     add\n", "51           good_click_softsearch_category_fake0/w:0           None      (800, 64)     add\n", "52           good_click_softsearch_category_fake1/b:0           None        (32, 1)     add\n", "53           good_click_softsearch_category_fake1/w:0           None       (64, 32)     add\n", "54         good_click_softsearch_item_count_fake0/b:0           None        (64, 1)     add\n", "55         good_click_softsearch_item_count_fake0/w:0           None      (400, 64)     add\n", "56         good_click_softsearch_item_count_fake1/b:0           None        (32, 1)     add\n", "57         good_click_softsearch_item_count_fake1/w:0           None       (64, 32)     add\n", "58            good_click_softsearch_item_id_fake0/b:0           None        (64, 1)     add\n", "59            good_click_softsearch_item_id_fake0/w:0           None     (1600, 64)     add\n", "60            good_click_softsearch_item_id_fake1/b:0           None        (32, 1)     add\n", "61            good_click_softsearch_item_id_fake1/w:0           None       (64, 32)     add\n", "62                good_click_softsearch_lag_fake0/b:0           None        (64, 1)     add\n", "63                good_click_softsearch_lag_fake0/w:0           None      (800, 64)     add\n", "64                good_click_softsearch_lag_fake1/b:0           None        (32, 1)     add\n", "65                good_click_softsearch_lag_fake1/w:0           None       (64, 32)     add\n", "66  good_click_softsearch_layer_new_0/LayerNorm/be...           None       (128, 1)     add\n", "67  good_click_softsearch_layer_new_0/LayerNorm/ga...           None       (128, 1)     add\n", "68              good_click_softsearch_layer_new_0/b:0           None       (128, 1)     add\n", "69              good_click_softsearch_layer_new_0/w:0           None      (72, 128)     add\n", "70  good_click_softsearch_layer_new_1/LayerNorm/be...           None        (64, 1)     add\n", "71  good_click_softsearch_layer_new_1/LayerNorm/ga...           None        (64, 1)     add\n", "72              good_click_softsearch_layer_new_1/b:0           None        (64, 1)     add\n", "73              good_click_softsearch_layer_new_1/w:0           None      (128, 64)     add\n", "74              good_click_softsearch_layer_new_2/b:0           None        (32, 1)     add\n", "75              good_click_softsearch_layer_new_2/w:0           None       (64, 32)     add\n", "76              good_click_softsearch_price_fake0/b:0           None        (64, 1)     add\n", "77              good_click_softsearch_price_fake0/w:0           None      (800, 64)     add\n", "78              good_click_softsearch_price_fake1/b:0           None        (32, 1)     add\n", "79              good_click_softsearch_price_fake1/w:0           None       (64, 32)     add\n", "80          good_click_softsearch_seller_id_fake0/b:0           None        (64, 1)     add\n", "81          good_click_softsearch_seller_id_fake0/w:0           None     (1600, 64)     add\n", "82          good_click_softsearch_seller_id_fake1/b:0           None        (32, 1)     add\n", "83          good_click_softsearch_seller_id_fake1/w:0           None       (64, 32)     add\n", "84       good_click_softsearch_topk_indices_fake0/b:0           None        (64, 1)     add\n", "85       good_click_softsearch_topk_indices_fake0/w:0           None      (400, 64)     add\n", "86       good_click_softsearch_topk_indices_fake1/b:0           None        (32, 1)     add\n", "87       good_click_softsearch_topk_indices_fake1/w:0           None       (64, 32)     add\n", "88        good_click_softsearch_topk_values_fake0/b:0           None        (64, 1)     add\n", "89        good_click_softsearch_topk_values_fake0/w:0           None      (400, 64)     add\n", "90        good_click_softsearch_topk_values_fake1/b:0           None        (32, 1)     add\n", "91        good_click_softsearch_topk_values_fake1/w:0           None       (64, 32)     add\n", "92        good_click_softsearch_user_embedding/beta:0           None      (7200, 1)     add\n", "93       good_click_softsearch_user_embedding/gamma:0           None      (7200, 1)     add\n", "Dense Extra Diff: \n", "                                                 name origin_shape current_shape    type\n", "0                 QUEUE_SOFT/share_bottom_layer_0/w:0   (8074240,)    (8139776,)  change\n", "1      good_click_cate2cate_attentionq_trans_matrix:0      (9216,)      (16384,)  change\n", "2   good_show_user_embedding_attentionq_trans_matr...      (9216,)      (16384,)  change\n", "3              good_show_user_embedding_pooling_0/w:0     (86016,)     (143360,)  change\n", "4                             goods_feat_layer/beta:0       (288,)        (512,)  change\n", "5                            goods_feat_layer/gamma:0       (288,)        (512,)  change\n", "6                        share_bottom_layer_new_0/w:0  (14232576,)   (14527488,)  change\n", "7                                 LayerNorm_57/beta:0         None         (32,)     add\n", "8                                LayerNorm_57/gamma:0         None         (32,)     add\n", "9                                 LayerNorm_58/beta:0         None         (32,)     add\n", "10                               LayerNorm_58/gamma:0         None         (32,)     add\n", "11                                LayerNorm_59/beta:0         None         (32,)     add\n", "12                               LayerNorm_59/gamma:0         None         (32,)     add\n", "13                                LayerNorm_60/beta:0         None         (32,)     add\n", "14                               LayerNorm_60/gamma:0         None         (32,)     add\n", "15                                LayerNorm_61/beta:0         None         (32,)     add\n", "16                               LayerNorm_61/gamma:0         None         (32,)     add\n", "17                                LayerNorm_62/beta:0         None         (32,)     add\n", "18                               LayerNorm_62/gamma:0         None         (32,)     add\n", "19                                LayerNorm_63/beta:0         None         (32,)     add\n", "20                               LayerNorm_63/gamma:0         None         (32,)     add\n", "21                                LayerNorm_64/beta:0         None         (32,)     add\n", "22                               LayerNorm_64/gamma:0         None         (32,)     add\n", "23                                LayerNorm_65/beta:0         None         (32,)     add\n", "24                               LayerNorm_65/gamma:0         None         (32,)     add\n", "25                                LayerNorm_66/beta:0         None         (32,)     add\n", "26                               LayerNorm_66/gamma:0         None         (32,)     add\n", "27                                LayerNorm_67/beta:0         None         (32,)     add\n", "28                               LayerNorm_67/gamma:0         None         (32,)     add\n", "29                                LayerNorm_68/beta:0         None         (32,)     add\n", "30                               LayerNorm_68/gamma:0         None         (32,)     add\n", "31                                LayerNorm_69/beta:0         None         (32,)     add\n", "32                               LayerNorm_69/gamma:0         None         (32,)     add\n", "33                                LayerNorm_70/beta:0         None         (32,)     add\n", "34                               LayerNorm_70/gamma:0         None         (32,)     add\n", "35                                LayerNorm_71/beta:0         None         (32,)     add\n", "36                               LayerNorm_71/gamma:0         None         (32,)     add\n", "37                                LayerNorm_72/beta:0         None         (32,)     add\n", "38                               LayerNorm_72/gamma:0         None         (32,)     add\n", "39                                LayerNorm_73/beta:0         None         (32,)     add\n", "40                               LayerNorm_73/gamma:0         None         (32,)     add\n", "41                                LayerNorm_74/beta:0         None         (32,)     add\n", "42                               LayerNorm_74/gamma:0         None         (32,)     add\n", "43    good_click_softsearch_attentionk_trans_matrix:0         None       (2304,)     add\n", "44    good_click_softsearch_attentionq_trans_matrix:0         None      (16384,)     add\n", "45    good_click_softsearch_attentionv_trans_matrix:0         None       (2304,)     add\n", "46         good_click_softsearch_carry_type_fake0/b:0         None         (64,)     add\n", "47         good_click_softsearch_carry_type_fake0/w:0         None      (25600,)     add\n", "48         good_click_softsearch_carry_type_fake1/b:0         None         (32,)     add\n", "49         good_click_softsearch_carry_type_fake1/w:0         None       (2048,)     add\n", "50           good_click_softsearch_category_fake0/b:0         None         (64,)     add\n", "51           good_click_softsearch_category_fake0/w:0         None      (51200,)     add\n", "52           good_click_softsearch_category_fake1/b:0         None         (32,)     add\n", "53           good_click_softsearch_category_fake1/w:0         None       (2048,)     add\n", "54         good_click_softsearch_item_count_fake0/b:0         None         (64,)     add\n", "55         good_click_softsearch_item_count_fake0/w:0         None      (25600,)     add\n", "56         good_click_softsearch_item_count_fake1/b:0         None         (32,)     add\n", "57         good_click_softsearch_item_count_fake1/w:0         None       (2048,)     add\n", "58            good_click_softsearch_item_id_fake0/b:0         None         (64,)     add\n", "59            good_click_softsearch_item_id_fake0/w:0         None     (102400,)     add\n", "60            good_click_softsearch_item_id_fake1/b:0         None         (32,)     add\n", "61            good_click_softsearch_item_id_fake1/w:0         None       (2048,)     add\n", "62                good_click_softsearch_lag_fake0/b:0         None         (64,)     add\n", "63                good_click_softsearch_lag_fake0/w:0         None      (51200,)     add\n", "64                good_click_softsearch_lag_fake1/b:0         None         (32,)     add\n", "65                good_click_softsearch_lag_fake1/w:0         None       (2048,)     add\n", "66  good_click_softsearch_layer_new_0/LayerNorm/be...         None        (128,)     add\n", "67  good_click_softsearch_layer_new_0/LayerNorm/ga...         None        (128,)     add\n", "68              good_click_softsearch_layer_new_0/b:0         None        (128,)     add\n", "69              good_click_softsearch_layer_new_0/w:0         None       (9216,)     add\n", "70  good_click_softsearch_layer_new_1/LayerNorm/be...         None         (64,)     add\n", "71  good_click_softsearch_layer_new_1/LayerNorm/ga...         None         (64,)     add\n", "72              good_click_softsearch_layer_new_1/b:0         None         (64,)     add\n", "73              good_click_softsearch_layer_new_1/w:0         None       (8192,)     add\n", "74              good_click_softsearch_layer_new_2/b:0         None         (32,)     add\n", "75              good_click_softsearch_layer_new_2/w:0         None       (2048,)     add\n", "76              good_click_softsearch_price_fake0/b:0         None         (64,)     add\n", "77              good_click_softsearch_price_fake0/w:0         None      (51200,)     add\n", "78              good_click_softsearch_price_fake1/b:0         None         (32,)     add\n", "79              good_click_softsearch_price_fake1/w:0         None       (2048,)     add\n", "80          good_click_softsearch_seller_id_fake0/b:0         None         (64,)     add\n", "81          good_click_softsearch_seller_id_fake0/w:0         None     (102400,)     add\n", "82          good_click_softsearch_seller_id_fake1/b:0         None         (32,)     add\n", "83          good_click_softsearch_seller_id_fake1/w:0         None       (2048,)     add\n", "84       good_click_softsearch_topk_indices_fake0/b:0         None         (64,)     add\n", "85       good_click_softsearch_topk_indices_fake0/w:0         None      (25600,)     add\n", "86       good_click_softsearch_topk_indices_fake1/b:0         None         (32,)     add\n", "87       good_click_softsearch_topk_indices_fake1/w:0         None       (2048,)     add\n", "88        good_click_softsearch_topk_values_fake0/b:0         None         (64,)     add\n", "89        good_click_softsearch_topk_values_fake0/w:0         None      (25600,)     add\n", "90        good_click_softsearch_topk_values_fake1/b:0         None         (32,)     add\n", "91        good_click_softsearch_topk_values_fake1/w:0         None       (2048,)     add\n", "92        good_click_softsearch_user_embedding/beta:0         None       (7200,)     add\n", "93       good_click_softsearch_user_embedding/gamma:0         None       (7200,)     add\n"]}, {"data": {"text/plain": ["([{'name': 'QUEUE_SOFT/share_bottom_layer_0/w:0',\n", "   'origin_shape': (7885, 1024),\n", "   'current_shape': (7949, 1024),\n", "   'type': 'change'},\n", "  {'name': 'good_click_cate2cate_attentionq_trans_matrix:0',\n", "   'origin_shape': (288, 32),\n", "   'current_shape': (512, 32),\n", "   'type': 'change'},\n", "  {'name': 'good_show_user_embedding_attentionq_trans_matrix:0',\n", "   'origin_shape': (288, 32),\n", "   'current_shape': (512, 32),\n", "   'type': 'change'},\n", "  {'name': 'good_show_user_embedding_pooling_0/w:0',\n", "   'origin_shape': (336, 256),\n", "   'current_shape': (560, 256),\n", "   'type': 'change'},\n", "  {'name': 'goods_feat_layer/beta:0',\n", "   'origin_shape': (288, 1),\n", "   'current_shape': (512, 1),\n", "   'type': 'change'},\n", "  {'name': 'goods_feat_layer/gamma:0',\n", "   'origin_shape': (288, 1),\n", "   'current_shape': (512, 1),\n", "   'type': 'change'},\n", "  {'name': 'share_bottom_layer_new_0/w:0',\n", "   'origin_shape': (13899, 1024),\n", "   'current_shape': (14187, 1024),\n", "   'type': 'change'},\n", "  {'name': 'LayerNorm_57/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_57/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_58/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_58/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_59/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_59/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_60/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_60/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_61/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_61/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_62/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_62/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_63/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_63/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_64/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_64/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_65/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_65/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_66/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_66/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_67/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_67/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_68/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_68/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_69/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_69/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_70/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_70/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_71/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_71/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_72/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_72/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_73/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_73/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_74/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_74/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionk_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (72, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionq_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (512, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionv_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (72, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (800, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (1600, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (800, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (72, 128),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_2/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_2/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (800, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (1600, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (400, 64),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 32),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_user_embedding/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (7200, 1),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_user_embedding/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (7200, 1),\n", "   'type': 'add'}],\n", " [{'name': 'QUEUE_SOFT/share_bottom_layer_0/w:0',\n", "   'origin_shape': (8074240,),\n", "   'current_shape': (8139776,),\n", "   'type': 'change'},\n", "  {'name': 'good_click_cate2cate_attentionq_trans_matrix:0',\n", "   'origin_shape': (9216,),\n", "   'current_shape': (16384,),\n", "   'type': 'change'},\n", "  {'name': 'good_show_user_embedding_attentionq_trans_matrix:0',\n", "   'origin_shape': (9216,),\n", "   'current_shape': (16384,),\n", "   'type': 'change'},\n", "  {'name': 'good_show_user_embedding_pooling_0/w:0',\n", "   'origin_shape': (86016,),\n", "   'current_shape': (143360,),\n", "   'type': 'change'},\n", "  {'name': 'goods_feat_layer/beta:0',\n", "   'origin_shape': (288,),\n", "   'current_shape': (512,),\n", "   'type': 'change'},\n", "  {'name': 'goods_feat_layer/gamma:0',\n", "   'origin_shape': (288,),\n", "   'current_shape': (512,),\n", "   'type': 'change'},\n", "  {'name': 'share_bottom_layer_new_0/w:0',\n", "   'origin_shape': (14232576,),\n", "   'current_shape': (14527488,),\n", "   'type': 'change'},\n", "  {'name': 'LayerNorm_57/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_57/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_58/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_58/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_59/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_59/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_60/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_60/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_61/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_61/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_62/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_62/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_63/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_63/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_64/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_64/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_65/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_65/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_66/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_66/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_67/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_67/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_68/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_68/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_69/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_69/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_70/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_70/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_71/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_71/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_72/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_72/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_73/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_73/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_74/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'LayerNorm_74/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionk_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2304,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionq_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (16384,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_attentionv_trans_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2304,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_carry_type_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (51200,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_category_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_count_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (102400,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_item_id_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (51200,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_lag_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (9216,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (8192,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_2/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_layer_new_2/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (51200,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_price_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (102400,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_seller_id_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_indices_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake0/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake0/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (25600,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_topk_values_fake1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (2048,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_user_embedding/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (7200,),\n", "   'type': 'add'},\n", "  {'name': 'good_click_softsearch_user_embedding/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (7200,),\n", "   'type': 'add'}])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["old_yaml = 'yipeng_cart_re_rb_mix'\n", "new_yaml = 'xiatian06_dsp_lps_sv_xt_good_click_soft_v2'\n", "\n", "old_yaml = os.path.join(dirpath, old_yaml+'.yaml')\n", "new_yaml = os.path.join(dirpath, new_yaml+'.yaml')\n", "dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)\n", "# print(dense_old)\n", "dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)\n", "# print(dense_new)\n", "diff_dense_table(dense_old, dense_new)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = None\n", "    extra = None\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 第一层的权重的尺寸可能不一样，取决于输入\n", "    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。\n", "    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。\n", "    \n", "    # 这里是旧权重尺寸小(或无）的情况\n", "    for var_name in list(tf_weight): \n", "        if var_name == 'QUEUE_SOFT/share_bottom_layer_0/w:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:7885, :] = ori_weight\n", "            new_extra[:7885, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name == 'share_bottom_layer_new_0/w:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行前半段赋值\n", "            new_weight[:8421, :] = ori_weight[:8421, :]\n", "            new_extra[:8421, :,:] = ori_extra[:8421, :,:]\n", "\n", "            # 进行后半段赋值\n", "            new_weight[8645:14123, :] = ori_weight[8421:, :]\n", "            new_extra[8645:14123, :,:] = ori_extra[8421:, :,:]\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name in [\"good_click_cate2cate_attentionq_trans_matrix:0\",'good_show_user_embedding_attentionq_trans_matrix:0']:\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:288, :] = ori_weight\n", "            new_extra[:288, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name in ['goods_feat_layer/beta:0','goods_feat_layer/gamma:0']:\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:288] = ori_weight\n", "            new_extra[:288, :] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name == 'good_show_user_embedding_pooling_0/w:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:336, :] = ori_weight\n", "            new_extra[:336, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name not in warmup_weight:\n", "            print(\"加载的 dense variable({}) 不存在，其值全新初始化\".format(var_name))\n", "            warmup_weight[var_name] = tf_weight[var_name]\n", "            warmup_extra[var_name] = ps_extra[var_name]\n", "\n", "            \n", "    if len(warmup_weight) > 0:\n", "        for var_name in list(warmup_weight):\n", "            if var_name not in tf_weight:\n", "                print(\"加载的 dense variable({}) 在运行时不存在，其值被忽略。\".format(var_name))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "#                     if var_name not in warmup_weight:\n", "#                         print(f\"{var_name} not in warmup_weight\")\n", "#                     else:\n", "#                         print(f\"{var_name} not in warmup_extra\")\n", "                    pass\n", "            elif warmup_weight[var_name].size != tf_weight[var_name].size:\n", "                # 这里可以添加旧权重尺寸大的处理逻辑\n", "                print(\"加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略\".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "                    if var_name not in warmup_weight:\n", "                        print(f\"{var_name} not in warmup_weight\")\n", "                    else:\n", "                        print(f\"{var_name} not in warmup_extra\")\n", "        weight = warmup_weight\n", "        extra = warmup_extra\n", "    else:\n", "        weight = tf_weight\n", "        extra = ps_extra\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["my_load_dense_func\n", "加载的 dense variable(goods_feat_layer/beta:0) 不存在，其值由goods_feat_layer/beta:0初始化, size is 512 and 512\n", "加载的 dense variable(goods_feat_layer/gamma:0) 不存在，其值由goods_feat_layer/gamma:0初始化, size is 512 and 512\n", "加载的 dense variable(good_click_softsearch_user_embedding/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_user_embedding/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_user_embedding_pooling_0/w:0) 不存在，其值由good_show_user_embedding_pooling_0/w:0初始化, size is 143360 and 143360\n", "加载的 dense variable(good_show_user_embedding_attentionq_trans_matrix:0) 不存在，其值由good_show_user_embedding_attentionq_trans_matrix:0初始化, size is 16384 and 16384\n", "加载的 dense variable(good_click_softsearch_item_id_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_id_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_id_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_id_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_seller_id_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_seller_id_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_seller_id_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_seller_id_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_lag_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_lag_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_lag_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_lag_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_category_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_category_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_category_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_category_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_carry_type_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_carry_type_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_carry_type_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_carry_type_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_price_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_price_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_price_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_price_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_57/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_57/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_58/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_58/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_fake0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_fake0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_fake1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_fake1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_59/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_59/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_60/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_60/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_61/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_61/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_62/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_62/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_63/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_63/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_64/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_64/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_65/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_65/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_66/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_66/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_67/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_67/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_68/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_68/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_69/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_69/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_70/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_70/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_71/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_71/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_72/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_72/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_73/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_73/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_74/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_74/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_cate2cate_attentionq_trans_matrix:0) 不存在，其值由good_click_cate2cate_attentionq_trans_matrix:0初始化, size is 16384 and 16384\n", "加载的 dense variable(good_click_softsearch_layer_new_0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_0/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_0/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_2/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_2/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_attentionq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_attentionk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_attentionv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(QUEUE_SOFT/share_bottom_layer_0/w:0) 不存在，其值由QUEUE_SOFT/share_bottom_layer_0/w:0初始化, size is 8139776 and 8139776\n", "加载的 dense variable(share_bottom_layer_new_0/w:0) 不存在，其值由share_bottom_layer_new_0/w:0初始化, size is 14527488 and 14527488\n", "end my_load_dense_func\n", "Dense Weight Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n", "Dense Extra Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n"]}, {"data": {"text/plain": ["([], [])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n", "dense_change = get_dense_table_from_dict(res_weight, res_extra)\n", "diff_dense_table(dense_new, dense_change)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}