s = 'cross_input, embedding_combine, long_att_output_prefix, dense_input, akg_pooling_emb, add_feas, new_fea, search_input, goods_input, item_add_feas, shark_emb_combine, u_p_i_input, cid_add_feas, add_new_fea, add_cpg_fea, add_adx_fea, shark_emb, llm_keywords_feas, keywords_emb, pinnerformer_input, fuse_pinnerformer_feat, add_rta_fea, utype_feas, cot_sparse, cot_dense, entity, candidate_att_output, video_atten_output, ln_photo_videoclip_emb, ln_user_videoclip_emb, moe_merge_dense, is_ai_tag, sft_dense, moe_sft_dense, multimodal_emb, coupon, match_0_dense, match_1_dense, match_2_dense, match_3_dense, match_4_dense, user_ecom_rq, ecom_multimodal_emb, ec_detail, commentstats, fuse_llm_other_fea, dpo, DenseDpoFea,long_sumpooling_input, good_click_cate2cate_seq_att_output, good_show_user_embedding_input, good_show_user_embedding_attention, good_show_soft_user_embedding_attention, eshop_ad, cart_photo_exposure_seq_att_output, good_click_cate2cate_att_output'
l = s.strip().split(',')
print(l[0],l[52],l[55])
