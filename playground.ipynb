{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-4035225266123964416 14411518807585587200\n", "-4035225266123964415 14411518807585587201\n", "-4035225266123964414 14411518807585587202\n", "-4035225266123964413 14411518807585587203\n", "-4035225266123964412 14411518807585587204\n"]}], "source": ["import numpy as np\n", "\n", "def main(n):\n", "    n = np.int64(n)\n", "    sign = np.uint64(n)\n", "    print(n,sign)\n", "\n", "for i in [-4035225266123964416, -4035225266123964415, -4035225266123964414, -4035225266123964413, -4035225266123964412]:\n", "    main(i)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0 [1.0, 0.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "0.1 [0.95, 0.05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "0.2 [0.9, 0.1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "0.3 [0.85, 0.15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "0.4 [0.8, 0.2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "0.5 [0.75, 0.25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "0.6 [0.7, 0.3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "0.7 [0.65, 0.35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "0.8 [0.6, 0.4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "0.9 [0.55, 0.45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "1.0 [0.5, 0.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "1.1 [0.44999999999999996, 0.55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "1.2 [0.4, 0.6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "1.3 [0.35, 0.65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "1.4 [0.30000000000000004, 0.7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "1.5 [0.25, 0.75, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "1.6 [0.19999999999999996, 0.8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "1.7 [0.15000000000000002, 0.85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "1.8 [0.09999999999999998, 0.9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "1.9 [0.050000000000000044, 0.95, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "2.0 [0, 0, 1.0, 0.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "2.1 [0, 0, 0.95, 0.050000000000000044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "2.2 [0, 0, 0.8999999999999999, 0.10000000000000009, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "2.3 [0, 0, 0.8500000000000001, 0.1499999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "2.4 [0, 0, 0.8, 0.19999999999999996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "2.5 [0, 0, 0.75, 0.25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "2.6 [0, 0, 0.7, 0.30000000000000004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "2.7 [0, 0, 0.6499999999999999, 0.3500000000000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "2.8 [0, 0, 0.6000000000000001, 0.3999999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "2.9 [0, 0, 0.55, 0.44999999999999996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "3.0 [0, 0, 0.5, 0.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "3.1 [0, 0, 0.44999999999999996, 0.55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "3.2 [0, 0, 0.3999999999999999, 0.6000000000000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "3.3 [0, 0, 0.3500000000000001, 0.6499999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "3.4 [0, 0, 0.30000000000000004, 0.7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "3.5 [0, 0, 0.25, 0.75, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "3.6 [0, 0, 0.19999999999999996, 0.8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "3.7 [0, 0, 0.1499999999999999, 0.8500000000000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "3.8 [0, 0, 0.10000000000000009, 0.8999999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "3.9 [0, 0, 0.050000000000000044, 0.95, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "4.0 [0, 0, 0, 0, 1.0, 0.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "4.1 [0, 0, 0, 0, 0.9500000000000002, 0.04999999999999982, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "4.2 [0, 0, 0, 0, 0.8999999999999999, 0.10000000000000009, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "4.3 [0, 0, 0, 0, 0.8500000000000001, 0.1499999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "4.4 [0, 0, 0, 0, 0.7999999999999998, 0.20000000000000018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "4.5 [0, 0, 0, 0, 0.75, 0.25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "4.6 [0, 0, 0, 0, 0.7000000000000002, 0.2999999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "4.7 [0, 0, 0, 0, 0.6499999999999999, 0.3500000000000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "4.8 [0, 0, 0, 0, 0.6000000000000001, 0.3999999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "4.9 [0, 0, 0, 0, 0.5499999999999998, 0.4500000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "5.0 [0, 0, 0, 0, 0.5, 0.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "5.1 [0, 0, 0, 0, 0.4500000000000002, 0.5499999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "5.2 [0, 0, 0, 0, 0.3999999999999999, 0.6000000000000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "5.3 [0, 0, 0, 0, 0.3500000000000001, 0.6499999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "5.4 [0, 0, 0, 0, 0.2999999999999998, 0.7000000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "5.5 [0, 0, 0, 0, 0.25, 0.75, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "5.6 [0, 0, 0, 0, 0.20000000000000018, 0.7999999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "5.7 [0, 0, 0, 0, 0.1499999999999999, 0.8500000000000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "5.8 [0, 0, 0, 0, 0.10000000000000009, 0.8999999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "5.9 [0, 0, 0, 0, 0.04999999999999982, 0.9500000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "6.0 [0, 0, 0, 0, 0, 0, 1.0, 0.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "6.1 [0, 0, 0, 0, 0, 0, 0.9500000000000002, 0.04999999999999982, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "6.2 [0, 0, 0, 0, 0, 0, 0.8999999999999999, 0.10000000000000009, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "6.3 [0, 0, 0, 0, 0, 0, 0.8500000000000001, 0.1499999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "6.4 [0, 0, 0, 0, 0, 0, 0.7999999999999998, 0.20000000000000018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "6.5 [0, 0, 0, 0, 0, 0, 0.75, 0.25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "6.6 [0, 0, 0, 0, 0, 0, 0.7000000000000002, 0.2999999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "6.7 [0, 0, 0, 0, 0, 0, 0.6499999999999999, 0.3500000000000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "6.8 [0, 0, 0, 0, 0, 0, 0.6000000000000001, 0.3999999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "6.9 [0, 0, 0, 0, 0, 0, 0.5499999999999998, 0.4500000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "7.0 [0, 0, 0, 0, 0, 0, 0.5, 0.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "7.1 [0, 0, 0, 0, 0, 0, 0.4500000000000002, 0.5499999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "7.2 [0, 0, 0, 0, 0, 0, 0.3999999999999999, 0.6000000000000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "7.3 [0, 0, 0, 0, 0, 0, 0.3500000000000001, 0.6499999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "7.4 [0, 0, 0, 0, 0, 0, 0.2999999999999998, 0.7000000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "7.5 [0, 0, 0, 0, 0, 0, 0.25, 0.75, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "7.6 [0, 0, 0, 0, 0, 0, 0.20000000000000018, 0.7999999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "7.7 [0, 0, 0, 0, 0, 0, 0.1499999999999999, 0.8500000000000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "7.8 [0, 0, 0, 0, 0, 0, 0.10000000000000009, 0.8999999999999999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "7.9 [0, 0, 0, 0, 0, 0, 0.04999999999999982, 0.9500000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "8.0 [0, 0, 0, 0, 0, 0, 0, 0, 1.0, 0.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "8.1 [0, 0, 0, 0, 0, 0, 0, 0, 0.9500000000000002, 0.04999999999999982, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "8.2 [0, 0, 0, 0, 0, 0, 0, 0, 0.9000000000000004, 0.09999999999999964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "8.3 [0, 0, 0, 0, 0, 0, 0, 0, 0.8499999999999996, 0.15000000000000036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "8.4 [0, 0, 0, 0, 0, 0, 0, 0, 0.7999999999999998, 0.20000000000000018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "8.5 [0, 0, 0, 0, 0, 0, 0, 0, 0.75, 0.25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "8.6 [0, 0, 0, 0, 0, 0, 0, 0, 0.7000000000000002, 0.2999999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "8.7 [0, 0, 0, 0, 0, 0, 0, 0, 0.6500000000000004, 0.34999999999999964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "8.8 [0, 0, 0, 0, 0, 0, 0, 0, 0.5999999999999996, 0.40000000000000036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "8.9 [0, 0, 0, 0, 0, 0, 0, 0, 0.5499999999999998, 0.4500000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "9.0 [0, 0, 0, 0, 0, 0, 0, 0, 0.5, 0.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "9.1 [0, 0, 0, 0, 0, 0, 0, 0, 0.4500000000000002, 0.5499999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "9.2 [0, 0, 0, 0, 0, 0, 0, 0, 0.40000000000000036, 0.5999999999999996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "9.3 [0, 0, 0, 0, 0, 0, 0, 0, 0.34999999999999964, 0.6500000000000004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "9.4 [0, 0, 0, 0, 0, 0, 0, 0, 0.2999999999999998, 0.7000000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "9.5 [0, 0, 0, 0, 0, 0, 0, 0, 0.25, 0.75, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "9.6 [0, 0, 0, 0, 0, 0, 0, 0, 0.20000000000000018, 0.7999999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "9.7 [0, 0, 0, 0, 0, 0, 0, 0, 0.15000000000000036, 0.8499999999999996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "9.8 [0, 0, 0, 0, 0, 0, 0, 0, 0.09999999999999964, 0.9000000000000004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "9.9 [0, 0, 0, 0, 0, 0, 0, 0, 0.04999999999999982, 0.9500000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "10.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0, 0.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "10.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9500000000000002, 0.04999999999999982, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "10.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9000000000000004, 0.09999999999999964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "10.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8499999999999996, 0.15000000000000036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "10.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.7999999999999998, 0.20000000000000018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "10.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.75, 0.25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "10.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.7000000000000002, 0.2999999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "10.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6500000000000004, 0.34999999999999964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "10.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5999999999999996, 0.40000000000000036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "10.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5499999999999998, 0.4500000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "11.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5, 0.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "11.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.4500000000000002, 0.5499999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "11.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40000000000000036, 0.5999999999999996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "11.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.34999999999999964, 0.6500000000000004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "11.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2999999999999998, 0.7000000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "11.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.25, 0.75, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "11.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.20000000000000018, 0.7999999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "11.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15000000000000036, 0.8499999999999996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "11.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.09999999999999964, 0.9000000000000004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "11.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04999999999999982, 0.9500000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "12.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0, 0.0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "12.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9500000000000002, 0.04999999999999982, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "12.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9000000000000004, 0.09999999999999964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "12.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8499999999999996, 0.15000000000000036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "12.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.7999999999999998, 0.20000000000000018, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "12.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.75, 0.25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "12.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.7000000000000002, 0.2999999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "12.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6500000000000004, 0.34999999999999964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "12.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5999999999999996, 0.40000000000000036, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "12.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5499999999999998, 0.4500000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "13.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5, 0.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "13.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.4500000000000002, 0.5499999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "13.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40000000000000036, 0.5999999999999996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "13.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.34999999999999964, 0.6500000000000004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "13.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2999999999999998, 0.7000000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "13.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.25, 0.75, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "13.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.20000000000000018, 0.7999999999999998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "13.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15000000000000036, 0.8499999999999996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "13.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.09999999999999964, 0.9000000000000004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "13.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04999999999999982, 0.9500000000000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "14.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0, 0.0, 0, 0, 0, 0, 0, 0, 0, 0]\n", "14.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9500000000000002, 0.04999999999999982, 0, 0, 0, 0, 0, 0, 0, 0]\n", "14.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9000000000000004, 0.09999999999999964, 0, 0, 0, 0, 0, 0, 0, 0]\n", "14.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8499999999999996, 0.15000000000000036, 0, 0, 0, 0, 0, 0, 0, 0]\n", "14.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.7999999999999998, 0.20000000000000018, 0, 0, 0, 0, 0, 0, 0, 0]\n", "14.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.75, 0.25, 0, 0, 0, 0, 0, 0, 0, 0]\n", "14.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.7000000000000002, 0.2999999999999998, 0, 0, 0, 0, 0, 0, 0, 0]\n", "14.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6500000000000004, 0.34999999999999964, 0, 0, 0, 0, 0, 0, 0, 0]\n", "14.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5999999999999996, 0.40000000000000036, 0, 0, 0, 0, 0, 0, 0, 0]\n", "14.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5499999999999998, 0.4500000000000002, 0, 0, 0, 0, 0, 0, 0, 0]\n", "15.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5, 0.5, 0, 0, 0, 0, 0, 0, 0, 0]\n", "15.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.4500000000000002, 0.5499999999999998, 0, 0, 0, 0, 0, 0, 0, 0]\n", "15.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40000000000000036, 0.5999999999999996, 0, 0, 0, 0, 0, 0, 0, 0]\n", "15.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.34999999999999964, 0.6500000000000004, 0, 0, 0, 0, 0, 0, 0, 0]\n", "15.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2999999999999998, 0.7000000000000002, 0, 0, 0, 0, 0, 0, 0, 0]\n", "15.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.25, 0.75, 0, 0, 0, 0, 0, 0, 0, 0]\n", "15.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.20000000000000018, 0.7999999999999998, 0, 0, 0, 0, 0, 0, 0, 0]\n", "15.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15000000000000036, 0.8499999999999996, 0, 0, 0, 0, 0, 0, 0, 0]\n", "15.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.09999999999999964, 0.9000000000000004, 0, 0, 0, 0, 0, 0, 0, 0]\n", "15.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.04999999999999982, 0.9500000000000002, 0, 0, 0, 0, 0, 0, 0, 0]\n", "16.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0, 0.0, 0, 0, 0, 0, 0, 0]\n", "16.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9499999999999993, 0.05000000000000071, 0, 0, 0, 0, 0, 0]\n", "16.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9000000000000004, 0.09999999999999964, 0, 0, 0, 0, 0, 0]\n", "16.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8499999999999996, 0.15000000000000036, 0, 0, 0, 0, 0, 0]\n", "16.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8000000000000007, 0.1999999999999993, 0, 0, 0, 0, 0, 0]\n", "16.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.75, 0.25, 0, 0, 0, 0, 0, 0]\n", "16.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6999999999999993, 0.3000000000000007, 0, 0, 0, 0, 0, 0]\n", "16.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6500000000000004, 0.34999999999999964, 0, 0, 0, 0, 0, 0]\n", "16.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5999999999999996, 0.40000000000000036, 0, 0, 0, 0, 0, 0]\n", "16.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5500000000000007, 0.4499999999999993, 0, 0, 0, 0, 0, 0]\n", "17.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5, 0.5, 0, 0, 0, 0, 0, 0]\n", "17.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.4499999999999993, 0.5500000000000007, 0, 0, 0, 0, 0, 0]\n", "17.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40000000000000036, 0.5999999999999996, 0, 0, 0, 0, 0, 0]\n", "17.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.34999999999999964, 0.6500000000000004, 0, 0, 0, 0, 0, 0]\n", "17.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.3000000000000007, 0.6999999999999993, 0, 0, 0, 0, 0, 0]\n", "17.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.25, 0.75, 0, 0, 0, 0, 0, 0]\n", "17.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.1999999999999993, 0.8000000000000007, 0, 0, 0, 0, 0, 0]\n", "17.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15000000000000036, 0.8499999999999996, 0, 0, 0, 0, 0, 0]\n", "17.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.09999999999999964, 0.9000000000000004, 0, 0, 0, 0, 0, 0]\n", "17.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.05000000000000071, 0.9499999999999993, 0, 0, 0, 0, 0, 0]\n", "18.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0, 0.0, 0, 0, 0, 0]\n", "18.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9499999999999993, 0.05000000000000071, 0, 0, 0, 0]\n", "18.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9000000000000004, 0.09999999999999964, 0, 0, 0, 0]\n", "18.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8499999999999996, 0.15000000000000036, 0, 0, 0, 0]\n", "18.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8000000000000007, 0.1999999999999993, 0, 0, 0, 0]\n", "18.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.75, 0.25, 0, 0, 0, 0]\n", "18.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6999999999999993, 0.3000000000000007, 0, 0, 0, 0]\n", "18.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6500000000000004, 0.34999999999999964, 0, 0, 0, 0]\n", "18.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5999999999999996, 0.40000000000000036, 0, 0, 0, 0]\n", "18.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5500000000000007, 0.4499999999999993, 0, 0, 0, 0]\n", "19.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5, 0.5, 0, 0, 0, 0]\n", "19.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.4499999999999993, 0.5500000000000007, 0, 0, 0, 0]\n", "19.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40000000000000036, 0.5999999999999996, 0, 0, 0, 0]\n", "19.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.34999999999999964, 0.6500000000000004, 0, 0, 0, 0]\n", "19.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.3000000000000007, 0.6999999999999993, 0, 0, 0, 0]\n", "19.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.25, 0.75, 0, 0, 0, 0]\n", "19.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.1999999999999993, 0.8000000000000007, 0, 0, 0, 0]\n", "19.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15000000000000036, 0.8499999999999996, 0, 0, 0, 0]\n", "19.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.09999999999999964, 0.9000000000000004, 0, 0, 0, 0]\n", "19.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.05000000000000071, 0.9499999999999993, 0, 0, 0, 0]\n", "20.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0, 0.0, 0, 0]\n", "20.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9499999999999993, 0.05000000000000071, 0, 0]\n", "20.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9000000000000004, 0.09999999999999964, 0, 0]\n", "20.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8499999999999996, 0.15000000000000036, 0, 0]\n", "20.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8000000000000007, 0.1999999999999993, 0, 0]\n", "20.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.75, 0.25, 0, 0]\n", "20.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6999999999999993, 0.3000000000000007, 0, 0]\n", "20.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6500000000000004, 0.34999999999999964, 0, 0]\n", "20.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5999999999999996, 0.40000000000000036, 0, 0]\n", "20.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5500000000000007, 0.4499999999999993, 0, 0]\n", "21.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5, 0.5, 0, 0]\n", "21.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.4499999999999993, 0.5500000000000007, 0, 0]\n", "21.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40000000000000036, 0.5999999999999996, 0, 0]\n", "21.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.34999999999999964, 0.6500000000000004, 0, 0]\n", "21.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.3000000000000007, 0.6999999999999993, 0, 0]\n", "21.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.25, 0.75, 0, 0]\n", "21.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.1999999999999993, 0.8000000000000007, 0, 0]\n", "21.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15000000000000036, 0.8499999999999996, 0, 0]\n", "21.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.09999999999999964, 0.9000000000000004, 0, 0]\n", "21.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.05000000000000071, 0.9499999999999993, 0, 0]\n", "22.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0, 0.0]\n", "22.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9499999999999993, 0.05000000000000071]\n", "22.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9000000000000004, 0.09999999999999964]\n", "22.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8499999999999996, 0.15000000000000036]\n", "22.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.8000000000000007, 0.1999999999999993]\n", "22.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.75, 0.25]\n", "22.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6999999999999993, 0.3000000000000007]\n", "22.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.6500000000000004, 0.34999999999999964]\n", "22.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5999999999999996, 0.40000000000000036]\n", "22.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5500000000000007, 0.4499999999999993]\n", "23.0 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5, 0.5]\n", "23.1 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.4499999999999993, 0.5500000000000007]\n", "23.2 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40000000000000036, 0.5999999999999996]\n", "23.3 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.34999999999999964, 0.6500000000000004]\n", "23.4 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.3000000000000007, 0.6999999999999993]\n", "23.5 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.25, 0.75]\n", "23.6 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.1999999999999993, 0.8000000000000007]\n", "23.7 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15000000000000036, 0.8499999999999996]\n", "23.8 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.09999999999999964, 0.9000000000000004]\n", "23.9 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.05000000000000071, 0.9499999999999993]\n"]}], "source": ["t= [i/10 for i in range(24*10)]\n", "\n", "def cal_route_weight(t):\n", "    last_idx = int(t//2 * 2) % 24\n", "    next_idx = int(last_idx + 1) % 24\n", "    ratio = t%2 / 2\n", "    weight = [0] * 24\n", "    weight[last_idx] = 1 - ratio\n", "    weight[next_idx] = ratio\n", "    return weight\n", "\n", "for i in t:\n", "    print(i, cal_route_weight(i))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["class=ExtractCombineSparseInnerOrderCvr, category=combine, field=611, size=0, topic_id=0\n", "class=ExtractUserSparseInnerOrderRankTopkPhotoId, category=combine, field=611, size=0, topic_id=0\n", "class=ExtractUserSparseInnerOrderRankTopkCvr, category=combine, field=611, size=0, topic_id=0\n", "class=ExtractUserSparseInnerOrderRankTopkCpr, category=combine, field=611, size=0, topic_id=0\n", "class=ExtractUserSparseInnerOrderRankTopkLvtr, category=combine, field=611, size=0, topic_id=0\n", "class=ExtractUserSparseInnerOrderRankTopkWtr, category=combine, field=611, size=0, topic_id=0\n"]}], "source": ["text = \"\"\"column_name=ExtractCombineSparseInnerOrderCvr, compression_type = None,column_type = List<Long>,feature_extractor_flag = true\n", "column_name=ExtractUserSparseInnerOrderRankTopkPhotoId,compression_type = None,column_type = List<Long>,feature_extractor_flag = true\n", "column_name=ExtractUserSparseInnerOrderRankTopkCvr,compression_type = None,column_type = List<Long>,feature_extractor_flag = true\n", "column_name=ExtractUserSparseInnerOrderRankTopkCpr,compression_type = None,column_type = List<Long>,feature_extractor_flag = true\n", "column_name=ExtractUserSparseInnerOrderRankTopkLvtr,compression_type = None,column_type = List<Long>,feature_extractor_flag = true\n", "column_name=ExtractUserSparseInnerOrderRankTopkWtr,compression_type = None,column_type = List<Long>,feature_extractor_flag = true\"\"\".splitlines()\n", "\n", "for t in text:\n", "    print(f\"class={t.split(',')[0].split('=')[-1]}, category=combine, field=611, size=0, topic_id=0\")\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_click_softsearch_real_seller_id_list_top100,\n", "good_click_softsearch_cate1_list_top100,\n", "good_click_softsearch_cate2_list_top100,\n", "good_click_softsearch_cate3_list_top100,\n", "good_click_softsearch_click_type_list_top100,\n", "good_click_softsearch_from_list_top100,\n", "good_click_softsearch_page_view_time_list_top100,\n", "good_click_softsearch_label_list_top100,\n", "good_click_softsearch_lag_hour_list_top100,\n", "good_click_softsearch_lag_min_list_top100,\n", "good_show_soft_category1,\n", "good_show_soft_category2,\n", "good_show_soft_category3,\n", "good_show_soft_category4,\n", "good_show_soft_hour_of_day,\n", "good_show_soft_seller_id,\n", "good_show_soft_leaf_category,\n", "good_show_soft_time_gap_hour,\n"]}, {"data": {"text/plain": ["18"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["text= \"\"\"column_name=good_click_softsearch_real_seller_id_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_cate1_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_cate2_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_cate3_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_click_type_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_from_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_page_view_time_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_label_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_lag_hour_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_lag_min_list_top100, compression_type=None, column_type=List<Long>\n", "column_name = good_show_soft_category1, compression_type = None, column_type = List<Long>\n", "column_name = good_show_soft_category2, compression_type = None, column_type = List<Long>\n", "column_name = good_show_soft_category3, compression_type = None, column_type = List<Long>\n", "column_name = good_show_soft_category4, compression_type = None, column_type = List<Long>\n", "column_name = good_show_soft_hour_of_day, compression_type = None, column_type = List<Long>\n", "column_name = good_show_soft_seller_id, compression_type = None, column_type = List<Long>\n", "column_name = good_show_soft_leaf_category, compression_type = None, column_type = List<Long>\n", "column_name = good_show_soft_time_gap_hour, compression_type = None, column_type = List<Long>\"\"\".splitlines()\n", "\n", "for t in text:\n", "    print(f\"{t.split(',')[0].split('=')[-1].strip()},\")\n", "len(text)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["83"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["s = \"\"\"sparse0, sparse1, sparse2, sparse3, sparse4, sparse5, sparse6, sparse7,sparse8, sparse9, sparse10,sparse11,sparse12, sparse13, sparse14, sparse15, SIM_G_SEQ_16_380, SIM_G_SEQ_16_381, SIM_G_SEQ_8_382, <PERSON><PERSON>_G_SEQ_8_383, SIM_G_SEQ_8_384, akg_combine_371, ExtractPhotoSharkEmbedding, ExtractItemSharkEmbedding, ExtractUserPinnerformerEmbedding, ExtractItemPinnerformerEmbedding, dense, cot_user, cot_photo, cot_dense, entity, is_ai_tag, ExtractUserDenseAdClick7dVideolClipEmb, ExtractUserDenseAdClick30dVideolClipEmb, ExtractUserDenseAdClick60dVideolClipEmb, ExtractUserDenseOrderpay7dVideolClipEmb, ExtractUserDenseOrderpay30dVideolClipEmb, ExtractUserDenseOrderpay60dVideolClipEmb, ExtractPhotoConcatKeywordVideoClip, reco_sparse, RECO_SIM_G_SEQ_16_380, RECO_SIM_G_SEQ_16_381, RECO_SIM_G_SEQ_8_382, RECO_SIM_G_SEQ_8_383, RECO_SIM_G_SEQ_8_384, reco_dense, sft_dense, reco_sft_dense, multimodal_emb, coupon, match_dense, user_ecom_rq, ecom_multimodal_emb, ec_detail, ExtractDensePhotoCommentStats, ExtractPhotoQcpxCouponAmt, dpo, ExtractUserDenseDpoFea,\n", "            good_click_cate2cate_real_price_list_extend, good_click_cate2cate_category_list_extend, good_click_cate2cate_carry_type_list_extend, good_click_cate2cate_lag_list_extend, good_click_cate2cate_item_id_list_extend, good_click_cate2cate_seller_id_list_extend,\n", "            colossus_rs_count_index_list,colossus_rs_item_id_list,colossus_rs_lagV1_list,colossus_rs_lagV2_list,colossus_rs_pagecode_id_list,colossus_rs_uniform_spu_id_list,\n", "            eshop_ad,\n", "            good_click_cate2cate_cate1_list_extend, good_click_cate2cate_lag_hour_list_extend, good_click_cate2cate_click_type_list_extend, good_click_cate2cate_index_list_extend, \n", "            cart_photo_exposure_pid_list_expand, cart_photo_exposure_aid_list_expand, cart_photo_exposure_duration_list_expand, cart_photo_exposure_play_time_list_expand, cart_photo_exposure_channel_list_expand, cart_photo_exposure_spu_id_list_expand, cart_photo_exposure_category_list_expand,\n", "            request_time_hour\"\"\".split(',')\n", "len(s)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[22191424512515, 23317615620954, 23317615620954, 23959999877582, 23444521027181, 22207542224046, 21578668853102, 24053945919483, 21765947400603, 21211897705494, 21211897705494, 24236034362185, 23350952701180, 23350952701180, 24393032079471, 24393032079471, 24538482737856, 23795042093290, 21824885568053, 21965652178971, 24209602837038, 20685424113641, 24475918632703, 24374148590371, 21170151035108, 21170151035108, 21751660769847]\n", "[22191424512515, 23317615620954, 23317615620954, 23959999877582, 23444521027181, 22207542224046, 21578668853102, 24053945919483, 21765947400603, 21211897705494, 21211897705494, 24236034362185, 23350952701180, 23350952701180, 24393032079471, 24393032079471, 24538482737856, 23795042093290, 21824885568053, 21965652178971, 24209602837038, 20685424113641, 24475918632703, 24374148590371, 21170151035108, 21170151035108, 21751660769847]\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["origin = [23317615620954, 24475918632703, 23317615620954, 23350952701180, 23350952701180, 24393032079471, 24393032079471, 24236034362185, 24374148590371, 24209602837038, 23444521027181, 21211897705494, 21211897705494, 24538482737856, 24053945919483, 21824885568053, 22207542224046, 21578668853102, 21965652178971, 23795042093290, 20685424113641, 22191424512515, 21751660769847, 23959999877582, 21765947400603, 22710951294491, 21170151035108, 21170151035108]\n", "a = [21, 0, 2, 23, 10, 16, 17, 14, 24, 12, 11, 7, 4, 3, 5, 6, 13, 19, 15, 18, 9, 20, 1, 8, 27, 26, 22, 64, 28, 30, 31, 29, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 96, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 112, 97, 98, 99]\n", "b = [origin[i] for i in a if i < 28]\n", "cnt = ([i for i in a if i < 28])\n", "ref = [22191424512515, 23317615620954, 23317615620954, 23959999877582, 23444521027181, 22207542224046, 21578668853102, 24053945919483, 21765947400603, 21211897705494, 21211897705494, 24236034362185, 23350952701180, 23350952701180, 24393032079471, 24393032079471, 24538482737856, 23795042093290, 21824885568053, 21965652178971, 24209602837038, 20685424113641, 24475918632703, 24374148590371, 21170151035108, 21170151035108, 21751660769847]\n", "print(b)\n", "print(ref)\n", "list(range(28))==sorted([21, 0, 2, 23, 10, 16, 17, 14, 24, 12, 11, 3, 4, 7, 5, 6, 13, 18, 19, 9, 15, 1, 20, 27, 26, 8, 22, 25])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["['good_click_item_id_list_top100',\n", " 'good_click_seller_id_list_top100',\n", " 'good_click_real_seller_id_list_top100',\n", " 'good_click_lag_list_top100',\n", " 'good_click_cate1_list_top100',\n", " 'good_click_cate2_list_top100',\n", " 'good_click_cate3_list_top100',\n", " 'good_click_category_list_top100',\n", " 'good_click_carry_type_list_top100',\n", " 'good_click_click_type_list_top100',\n", " 'good_click_from_list_top100',\n", " 'good_click_price_list_top100',\n", " 'good_click_page_view_time_list_top100',\n", " 'good_click_label_list_top100',\n", " 'good_click_uniform_spu_id_list_top100',\n", " 'good_click_item_count_list',\n", " 'good_click_lag_hour_list_top100',\n", " 'good_click_lag_min_list_top100',\n", " 'good_click_seller_id_lag_list_top100',\n", " 'good_click_cate1_lag_list_top100',\n", " 'good_click_cate2_lag_list_top100',\n", " 'good_click_cate3_lag_list_top100',\n", " 'good_click_seller_id_price_list_top100',\n", " 'good_click_cate1_price_list_top100',\n", " 'good_click_cate2_price_list_top100',\n", " 'good_click_cate3_price_list_top100',\n", " 'good_click_seller_id_view_list_top100',\n", " 'good_click_cate1_view_list_top100',\n", " 'good_click_cate2_view_list_top100',\n", " 'good_click_cate3_view_list_top100']"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["a = \"\"\"new_good_click_item_id_list_extend, new_good_click_seller_id_list_extend, new_good_click_real_seller_id_list_extend, new_good_click_lag_list_extend,\n", "                new_good_click_cate1_list_extend, new_good_click_cate2_list_extend,\n", "                new_good_click_cate3_list_extend, new_good_click_category_list_extend,new_good_click_carry_type_list_extend, new_good_click_click_type_list_extend,\n", "                new_good_click_from_list_extend, new_good_click_price_list_extend,\n", "                new_good_click_page_view_time_list_extend, new_good_click_label_list_extend,\n", "                new_good_click_uniform_spu_id_list_extend, new_good_click_item_count_list,\n", "                new_good_click_lag_hour_list_extend, new_good_click_lag_min_list_extend,\n", "                new_good_click_seller_id_lag_list_extend, new_good_click_cate1_lag_list_extend, new_good_click_cate2_lag_list_extend, new_good_click_cate3_lag_list_extend,\n", "                new_good_click_seller_id_price_list_extend, new_good_click_cate1_price_list_extend, new_good_click_cate2_price_list_extend, new_good_click_cate3_price_list_extend,\n", "                new_good_click_seller_id_view_list_extend, new_good_click_cate1_view_list_extend, new_good_click_cate2_view_list_extend, new_good_click_cate3_view_list_extend\n", "            \"\"\".split(\",\")\n", "res = []\n", "for t in a:\n", "    res.append(t.strip().replace('new_','').replace('\\n','').replace('_list_extend','_list_top100'))\n", "res"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"\"function cal_price(ori_price)\n", "            local res = 0\n", "            res = ori_price // 100\n", "            if res >= 300 then res = 300\n", "            end\n", "            if res <0 then res = 0\n", "            end\n", "            return res\n", "        end\n", "\n", "        function cal_diff_price(ori_price, price)\n", "            if ori_price == 0 then return 0\n", "            end\n", "            local slice = 100\n", "            local res = ori_price - price\n", "            res = res * slice // ori_price\n", "            if res <0 then res = 0\n", "            end\n", "            if res >slice then res = slice\n", "            end\n", "            return res\n", "        end\n", "\n", "        function cal_view_time(view_time)\n", "            local res = view_time\n", "            if res <0 then res = 0\n", "            end\n", "            if res > 60 then res = 60\n", "            end\n", "            return res\n", "        end\n", "\n", "        function cal_cross(item1, item2)\n", "            return tostring(item1)..\"_\"..tostring(item2)\n", "        end\n", "\n", "        function calculate()\n", "            local good_click_cate2cate_cate1_list = good_click_cate2cate_cate1_list or {}\n", "            local good_click_cate2cate_cate2_list = good_click_cate2cate_cate2_list or {}\n", "            local good_click_cate2cate_cate3_list = good_click_cate2cate_cate3_list or {}\n", "            local good_click_cate2cate_category_list = good_click_cate2cate_category_list or {}\n", "            local good_click_cate2cate_click_from_list = good_click_cate2cate_click_from_list or {}\n", "            local good_click_cate2cate_click_index_list = good_click_cate2cate_click_index_list or {}\n", "            local good_click_cate2cate_item_id_list = good_click_cate2cate_item_id_list or {}\n", "            local good_click_cate2cate_lag_list = good_click_cate2cate_lag_list or {}\n", "            local good_click_cate2cate_real_price_list = good_click_cate2cate_real_price_list or {}\n", "            local good_click_cate2cate_real_seller_id_list = good_click_cate2cate_real_seller_id_list or {}\n", "            local good_click_cate2cate_seller_id_list = good_click_cate2cate_seller_id_list or {}\n", "            local good_click_cate2cate_timestamp_list = good_click_cate2cate_timestamp_list or {}\n", "\n", "            local new_good_click_cate2cate_item_id_list_extend = {}\n", "            local new_good_click_cate2cate_seller_id_list_extend = {}\n", "            local new_good_click_cate2cate_real_seller_id_list_extend = {}\n", "            local new_good_click_cate2cate_lag_list_extend = {}\n", "            local new_good_click_cate2cate_cate1_list_extend = {}\n", "            local new_good_click_cate2cate_cate2_list_extend = {}\n", "            local new_good_click_cate2cate_cate3_list_extend = {}\n", "            local new_good_click_cate2cate_category_list_extend = {}\n", "            local new_good_click_cate2cate_carry_type_list_extend = {}\n", "            local new_good_click_cate2cate_click_type_list_extend = {}\n", "            local new_good_click_cate2cate_click_from_list_extend = {}\n", "            local new_good_click_cate2cate_real_price_list_extend = {}\n", "            local new_good_click_cate2cate_index_list_extend = {}\n", "            local new_good_click_cate2cate_lag_hour_list_extend = {}\n", "            local new_good_click_cate2cate_lag_min_list_extend = {}\n", "            local new_good_click_cate2cate_seller_id_lag_list_extend = {}\n", "            local new_good_click_cate2cate_cate1_lag_list_extend = {}\n", "            local new_good_click_cate2cate_cate2_lag_list_extend = {}\n", "            local new_good_click_cate2cate_cate3_lag_list_extend = {}\n", "            local new_good_click_cate2cate_seller_id_price_list_extend = {}\n", "            local new_good_click_cate2cate_cate1_price_list_extend = {}\n", "            local new_good_click_cate2cate_cate2_price_list_extend = {}\n", "            local new_good_click_cate2cate_cate3_price_list_extend = {}\n", "\n", "            local count = 0\n", "            local len = #good_click_cate2cate_item_id_list\n", "            for index=len,1,-1 do\n", "                table.insert(new_good_click_cate2cate_item_id_list_extend, good_click_cate2cate_item_id_list[index])\n", "                table.insert(new_good_click_cate2cate_seller_id_list_extend, good_click_cate2cate_seller_id_list[index])\n", "                table.insert(new_good_click_cate2cate_real_seller_id_list_extend, good_click_cate2cate_real_seller_id_list[index])\n", "                table.insert(new_good_click_cate2cate_lag_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24))\n", "                table.insert(new_good_click_cate2cate_cate1_list_extend, good_click_cate2cate_cate1_list[index])\n", "                table.insert(new_good_click_cate2cate_cate2_list_extend, good_click_cate2cate_cate2_list[index])\n", "                table.insert(new_good_click_cate2cate_cate3_list_extend, good_click_cate2cate_cate3_list[index])\n", "                table.insert(new_good_click_cate2cate_category_list_extend, good_click_cate2cate_category_list[index])\n", "                table.insert(new_good_click_cate2cate_carry_type_list_extend, (good_click_cate2cate_click_flow_type_list[index]>>16) & 0xff)\n", "                table.insert(new_good_click_cate2cate_click_type_list_extend, (good_click_cate2cate_click_flow_type_list[index]>>24) & 0xff)\n", "                table.insert(new_good_click_cate2cate_click_from_list_extend, good_click_cate2cate_click_from_list[index])\n", "                table.insert(new_good_click_cate2cate_real_price_list_extend, cal_price(good_click_cate2cate_real_price_list[index]))\n", "                table.insert(new_good_click_cate2cate_index_list_extend, count)\n", "                table.insert(new_good_click_cate2cate_lag_hour_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600))\n", "                table.insert(new_good_click_cate2cate_lag_min_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(60))\n", "                table.insert(new_good_click_cate2cate_seller_id_lag_list_extend, cal_cross(good_click_cate2cate_seller_id_list[index], (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\n", "                table.insert(new_good_click_cate2cate_cate1_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 48) & 0xffff,\n", "                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\n", "                table.insert(new_good_click_cate2cate_cate2_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 32) & 0xffff,\n", "                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\n", "                table.insert(new_good_click_cate2cate_cate3_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 16) & 0xffff,\n", "                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\n", "                table.insert(new_good_click_cate2cate_seller_id_price_list_extend, cal_cross(good_click_cate2cate_seller_id_list[index], cal_price(good_click_cate2cate_real_price_list[index])))\n", "                table.insert(new_good_click_cate2cate_cate1_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 48) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))\n", "                table.insert(new_good_click_cate2cate_cate2_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 32) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))\n", "                table.insert(new_good_click_cate2cate_cate3_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 16) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))\n", "\n", "                count = count + 1\n", "                if count >= 100 then\n", "                    break\n", "                end\n", "            end\n", "\n", "            return new_good_click_cate2cate_item_id_list_extend, new_good_click_cate2cate_seller_id_list_extend, new_good_click_cate2cate_real_seller_id_list_extend,\n", "                new_good_click_cate2cate_lag_list_extend, new_good_click_cate2cate_cate1_list_extend, new_good_click_cate2cate_cate2_list_extend,\n", "                new_good_click_cate2cate_cate3_list_extend, new_good_click_cate2cate_category_list_extend, new_good_click_cate2cate_carry_type_list_extend,\n", "                new_good_click_cate2cate_click_type_list_extend, new_good_click_cate2cate_click_from_list_extend, new_good_click_cate2cate_real_price_list_extend,\n", "                new_good_click_cate2cate_index_list_extend, new_good_click_cate2cate_lag_hour_list_extend, new_good_click_cate2cate_lag_min_list_extend,\n", "                new_good_click_cate2cate_seller_id_lag_list_extend, new_good_click_cate2cate_cate1_lag_list_extend, new_good_click_cate2cate_cate2_lag_list_extend,\n", "                new_good_click_cate2cate_cate3_lag_list_extend, new_good_click_cate2cate_seller_id_price_list_extend, new_good_click_cate2cate_cate1_price_list_extend,\n", "                new_good_click_cate2cate_cate2_price_list_extend, new_good_click_cate2cate_cate3_price_list_extend\n", "        end\",\n", "           \n"]}], "source": ["print(\"\"\"\"\"function cal_price(ori_price)\\n            local res = 0\\n            res = ori_price // 100\\n            if res >= 300 then res = 300\\n            end\\n            if res <0 then res = 0\\n            end\\n            return res\\n        end\\n\\n        function cal_diff_price(ori_price, price)\\n            if ori_price == 0 then return 0\\n            end\\n            local slice = 100\\n            local res = ori_price - price\\n            res = res * slice // ori_price\\n            if res <0 then res = 0\\n            end\\n            if res >slice then res = slice\\n            end\\n            return res\\n        end\\n\\n        function cal_view_time(view_time)\\n            local res = view_time\\n            if res <0 then res = 0\\n            end\\n            if res > 60 then res = 60\\n            end\\n            return res\\n        end\\n\\n        function cal_cross(item1, item2)\\n            return tostring(item1)..\\\"_\\\"..tostring(item2)\\n        end\\n\\n        function calculate()\\n            local good_click_cate2cate_cate1_list = good_click_cate2cate_cate1_list or {}\\n            local good_click_cate2cate_cate2_list = good_click_cate2cate_cate2_list or {}\\n            local good_click_cate2cate_cate3_list = good_click_cate2cate_cate3_list or {}\\n            local good_click_cate2cate_category_list = good_click_cate2cate_category_list or {}\\n            local good_click_cate2cate_click_from_list = good_click_cate2cate_click_from_list or {}\\n            local good_click_cate2cate_click_index_list = good_click_cate2cate_click_index_list or {}\\n            local good_click_cate2cate_item_id_list = good_click_cate2cate_item_id_list or {}\\n            local good_click_cate2cate_lag_list = good_click_cate2cate_lag_list or {}\\n            local good_click_cate2cate_real_price_list = good_click_cate2cate_real_price_list or {}\\n            local good_click_cate2cate_real_seller_id_list = good_click_cate2cate_real_seller_id_list or {}\\n            local good_click_cate2cate_seller_id_list = good_click_cate2cate_seller_id_list or {}\\n            local good_click_cate2cate_timestamp_list = good_click_cate2cate_timestamp_list or {}\\n\\n            local new_good_click_cate2cate_item_id_list_extend = {}\\n            local new_good_click_cate2cate_seller_id_list_extend = {}\\n            local new_good_click_cate2cate_real_seller_id_list_extend = {}\\n            local new_good_click_cate2cate_lag_list_extend = {}\\n            local new_good_click_cate2cate_cate1_list_extend = {}\\n            local new_good_click_cate2cate_cate2_list_extend = {}\\n            local new_good_click_cate2cate_cate3_list_extend = {}\\n            local new_good_click_cate2cate_category_list_extend = {}\\n            local new_good_click_cate2cate_carry_type_list_extend = {}\\n            local new_good_click_cate2cate_click_type_list_extend = {}\\n            local new_good_click_cate2cate_click_from_list_extend = {}\\n            local new_good_click_cate2cate_real_price_list_extend = {}\\n            local new_good_click_cate2cate_index_list_extend = {}\\n            local new_good_click_cate2cate_lag_hour_list_extend = {}\\n            local new_good_click_cate2cate_lag_min_list_extend = {}\\n            local new_good_click_cate2cate_seller_id_lag_list_extend = {}\\n            local new_good_click_cate2cate_cate1_lag_list_extend = {}\\n            local new_good_click_cate2cate_cate2_lag_list_extend = {}\\n            local new_good_click_cate2cate_cate3_lag_list_extend = {}\\n            local new_good_click_cate2cate_seller_id_price_list_extend = {}\\n            local new_good_click_cate2cate_cate1_price_list_extend = {}\\n            local new_good_click_cate2cate_cate2_price_list_extend = {}\\n            local new_good_click_cate2cate_cate3_price_list_extend = {}\\n\\n            local count = 0\\n            local len = #good_click_cate2cate_item_id_list\\n            for index=len,1,-1 do\\n                table.insert(new_good_click_cate2cate_item_id_list_extend, good_click_cate2cate_item_id_list[index])\\n                table.insert(new_good_click_cate2cate_seller_id_list_extend, good_click_cate2cate_seller_id_list[index])\\n                table.insert(new_good_click_cate2cate_real_seller_id_list_extend, good_click_cate2cate_real_seller_id_list[index])\\n                table.insert(new_good_click_cate2cate_lag_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24))\\n                table.insert(new_good_click_cate2cate_cate1_list_extend, good_click_cate2cate_cate1_list[index])\\n                table.insert(new_good_click_cate2cate_cate2_list_extend, good_click_cate2cate_cate2_list[index])\\n                table.insert(new_good_click_cate2cate_cate3_list_extend, good_click_cate2cate_cate3_list[index])\\n                table.insert(new_good_click_cate2cate_category_list_extend, good_click_cate2cate_category_list[index])\\n                table.insert(new_good_click_cate2cate_carry_type_list_extend, (good_click_cate2cate_click_flow_type_list[index]>>16) & 0xff)\\n                table.insert(new_good_click_cate2cate_click_type_list_extend, (good_click_cate2cate_click_flow_type_list[index]>>24) & 0xff)\\n                table.insert(new_good_click_cate2cate_click_from_list_extend, good_click_cate2cate_click_from_list[index])\\n                table.insert(new_good_click_cate2cate_real_price_list_extend, cal_price(good_click_cate2cate_real_price_list[index]))\\n                table.insert(new_good_click_cate2cate_index_list_extend, count)\\n                table.insert(new_good_click_cate2cate_lag_hour_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600))\\n                table.insert(new_good_click_cate2cate_lag_min_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(60))\\n                table.insert(new_good_click_cate2cate_seller_id_lag_list_extend, cal_cross(good_click_cate2cate_seller_id_list[index], (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\\n                table.insert(new_good_click_cate2cate_cate1_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 48) & 0xffff,\\n                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\\n                table.insert(new_good_click_cate2cate_cate2_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 32) & 0xffff,\\n                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\\n                table.insert(new_good_click_cate2cate_cate3_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 16) & 0xffff,\\n                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))\\n                table.insert(new_good_click_cate2cate_seller_id_price_list_extend, cal_cross(good_click_cate2cate_seller_id_list[index], cal_price(good_click_cate2cate_real_price_list[index])))\\n                table.insert(new_good_click_cate2cate_cate1_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 48) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))\\n                table.insert(new_good_click_cate2cate_cate2_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 32) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))\\n                table.insert(new_good_click_cate2cate_cate3_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 16) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))\\n\\n                count = count + 1\\n                if count >= 100 then\\n                    break\\n                end\\n            end\\n\\n            return new_good_click_cate2cate_item_id_list_extend, new_good_click_cate2cate_seller_id_list_extend, new_good_click_cate2cate_real_seller_id_list_extend,\\n                new_good_click_cate2cate_lag_list_extend, new_good_click_cate2cate_cate1_list_extend, new_good_click_cate2cate_cate2_list_extend,\\n                new_good_click_cate2cate_cate3_list_extend, new_good_click_cate2cate_category_list_extend, new_good_click_cate2cate_carry_type_list_extend,\\n                new_good_click_cate2cate_click_type_list_extend, new_good_click_cate2cate_click_from_list_extend, new_good_click_cate2cate_real_price_list_extend,\\n                new_good_click_cate2cate_index_list_extend, new_good_click_cate2cate_lag_hour_list_extend, new_good_click_cate2cate_lag_min_list_extend,\\n                new_good_click_cate2cate_seller_id_lag_list_extend, new_good_click_cate2cate_cate1_lag_list_extend, new_good_click_cate2cate_cate2_lag_list_extend,\\n                new_good_click_cate2cate_cate3_lag_list_extend, new_good_click_cate2cate_seller_id_price_list_extend, new_good_click_cate2cate_cate1_price_list_extend,\\n                new_good_click_cate2cate_cate2_price_list_extend, new_good_click_cate2cate_cate3_price_list_extend\\n        end\",\n", "           \"\"\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_show_1011 colossus_rs_category_1_list\n", "good_show_1012 colossus_rs_category_2_list\n", "good_show_1013 colossus_rs_category_3_list\n", "good_show_1014 colossus_rs_count_index_list\n", "good_show_1015 colossus_rs_is_buy_list\n", "good_show_1016 colossus_rs_is_click_list\n", "good_show_1017 colossus_rs_item_id_list\n", "good_show_1018 colossus_rs_lagV1_list\n", "good_show_1019 colossus_rs_lagV2_list\n", "good_show_1020 colossus_rs_pagecode_id_list\n", "good_show_1021 colossus_rs_seller_id_list\n", "good_show_1022 colossus_rs_uniform_spu_id_list\n"]}], "source": ["a = [            {\n", "              \"from_common\": \"good_show_1011\",\n", "              \"to_common\": \"colossus_rs_category_1_list\"\n", "            },\n", "            {\n", "              \"from_common\": \"good_show_1012\",\n", "              \"to_common\": \"colossus_rs_category_2_list\"\n", "            },\n", "            {\n", "              \"from_common\": \"good_show_1013\",\n", "              \"to_common\": \"colossus_rs_category_3_list\"\n", "            },\n", "            {\n", "              \"from_common\": \"good_show_1014\",\n", "              \"to_common\": \"colossus_rs_count_index_list\"\n", "            },\n", "            {\n", "              \"from_common\": \"good_show_1015\",\n", "              \"to_common\": \"colossus_rs_is_buy_list\"\n", "            },\n", "            {\n", "              \"from_common\": \"good_show_1016\",\n", "              \"to_common\": \"colossus_rs_is_click_list\"\n", "            },\n", "            {\n", "              \"from_common\": \"good_show_1017\",\n", "              \"to_common\": \"colossus_rs_item_id_list\"\n", "            },\n", "            {\n", "              \"from_common\": \"good_show_1018\",\n", "              \"to_common\": \"colossus_rs_lagV1_list\"\n", "            },\n", "            {\n", "              \"from_common\": \"good_show_1019\",\n", "              \"to_common\": \"colossus_rs_lagV2_list\"\n", "            },\n", "            {\n", "              \"from_common\": \"good_show_1020\",\n", "              \"to_common\": \"colossus_rs_pagecode_id_list\"\n", "            },\n", "            {\n", "              \"from_common\": \"good_show_1021\",\n", "              \"to_common\": \"colossus_rs_seller_id_list\"\n", "            },\n", "            {\n", "              \"from_common\": \"good_show_1022\",\n", "              \"to_common\": \"colossus_rs_uniform_spu_id_list\"\n", "            }\n", "          ]\n", "for d in a:\n", "\n", "    print(d['from_common'], d['to_common'])"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["column_name=good_click_item_id_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_seller_id_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_real_seller_id_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_lag_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_carry_type_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_click_type_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_category_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_from_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_price_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_detail_page_view_time_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_origin_price_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_label_list_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_topk_indices_softsearch, compression_type=None, column_type=List<Long>\n", "column_name=good_click_topk_values_softsearch, compression_type=None, column_type=List<Long>\n"]}], "source": ["a = [\n", "            {\n", "              \"from_item\": \"good_click_top100_840\",\n", "              \"to_item\": \"good_click_item_id_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"good_click_top100_841\",\n", "              \"to_item\": \"good_click_seller_id_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"good_click_top100_842\",\n", "              \"to_item\": \"good_click_real_seller_id_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"good_click_top100_843\",\n", "              \"to_item\": \"good_click_lag_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"good_click_top100_844\",\n", "              \"to_item\": \"good_click_carry_type_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"good_click_top100_845\",\n", "              \"to_item\": \"good_click_click_type_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"good_click_top100_846\",\n", "              \"to_item\": \"good_click_category_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"good_click_top100_848\",\n", "              \"to_item\": \"good_click_from_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"good_click_top100_848\",\n", "              \"to_item\": \"good_click_price_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"good_click_top100_849\",\n", "              \"to_item\": \"good_detail_page_view_time_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"good_click_top100_850\",\n", "              \"to_item\": \"good_click_origin_price_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"good_click_top100_851\",\n", "              \"to_item\": \"good_click_label_list_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"topk_indices\",\n", "              \"to_item\": \"good_click_topk_indices_softsearch\"\n", "            },\n", "            {\n", "              \"from_item\": \"topk_values\",\n", "              \"to_item\": \"good_click_topk_values_softsearch\"\n", "            }\n", "          ]\n", "for i in a:\n", "    print(f\"column_name={i['to_item']}, compression_type=None, column_type=List<Long>\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'from_item': 'good_click_cate2cate_895',\n", "  'to_item': 'good_click_cate2cate_cate1_list_extend_copy'},\n", " {'from_item': 'good_click_cate2cate_896',\n", "  'to_item': 'good_click_cate2cate_cate2_list_extend_copy'},\n", " {'from_item': 'good_click_cate2cate_897',\n", "  'to_item': 'good_click_cate2cate_cate3_list_extend_copy'}]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["t = [\"good_click_cate2cate_cate1_list_extend\",\n", "            \"good_click_cate2cate_cate2_list_extend\",\n", "            \"good_click_cate2cate_cate3_list_extend\",]\n", "res = []\n", "for i in t:\n", "    res.append({'from_item': i, 'to_item':i.replace('_extend', '_extend_copy')})\n", "\n", "\n", "res = []\n", "for i in range(len(t)):\n", "    res.append({'from_item': f\"good_click_cate2cate_{895+i}\", 'to_item':t[i].replace('_extend', '_extend_copy')})\n", "\n", "res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'from_common': 'good_click_item_id_list',\n", "  'to_common': 'good_click_item_id_list_reverse'},\n", " {'from_common': 'good_click_seller_id_list',\n", "  'to_common': 'good_click_seller_id_list_reverse'},\n", " {'from_common': 'good_click_real_seller_id_list',\n", "  'to_common': 'good_click_real_seller_id_list_reverse'},\n", " {'from_common': 'good_click_timestamp_list',\n", "  'to_common': 'good_click_timestamp_list_reverse'},\n", " {'from_common': 'good_click_category_list',\n", "  'to_common': 'good_click_category_list_reverse'},\n", " {'from_common': 'good_click_flow_type_list',\n", "  'to_common': 'good_click_flow_type_list_reverse'},\n", " {'from_common': 'good_click_from_list',\n", "  'to_common': 'good_click_from_list_reverse'},\n", " {'from_common': 'good_click_price_list',\n", "  'to_common': 'good_click_price_list_reverse'},\n", " {'from_common': 'good_detail_page_view_time_list',\n", "  'to_common': 'good_detail_page_view_time_list_reverse'},\n", " {'from_common': 'good_click_origin_price_list',\n", "  'to_common': 'good_click_origin_price_list_reverse'},\n", " {'from_common': 'good_click_label_list',\n", "  'to_common': 'good_click_label_list_reverse'}]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["t = [\"good_click_item_id_list\",\n", "\"good_click_seller_id_list\",\n", "\"good_click_real_seller_id_list\",\n", "\"good_click_timestamp_list\",\n", "\"good_click_category_list\",\n", "\"good_click_flow_type_list\",\n", "\"good_click_from_list\",\n", "\"good_click_price_list\",\n", "\"good_detail_page_view_time_list\",\n", "\"good_click_origin_price_list\",\n", "\"good_click_label_list\",]\n", "res = []\n", "for i in t:\n", "    res.append({'from_common': i, 'to_common':i.replace('_list', '_list_reverse')})\n", "\n", "res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'from_item': 'good_click_top100_840',\n", "  'to_item': 'good_click_item_id_list_softsearch'},\n", " {'from_item': 'good_click_top100_841',\n", "  'to_item': 'good_click_seller_id_list_softsearch'},\n", " {'from_item': 'good_click_top100_842',\n", "  'to_item': 'good_click_real_seller_id_list_softsearch'},\n", " {'from_item': 'good_click_top100_843',\n", "  'to_item': 'good_click_lag_list_softsearch'},\n", " {'from_item': 'good_click_top100_844',\n", "  'to_item': 'good_click_carry_type_list_softsearch'},\n", " {'from_item': 'good_click_top100_845',\n", "  'to_item': 'good_click_click_type_list_softsearch'},\n", " {'from_item': 'good_click_top100_846',\n", "  'to_item': 'good_click_category_list_softsearch'},\n", " {'from_item': 'good_click_top100_848',\n", "  'to_item': 'good_click_from_list_softsearch'},\n", " {'from_item': 'good_click_top100_848',\n", "  'to_item': 'good_click_price_list_softsearch'},\n", " {'from_item': 'good_click_top100_849',\n", "  'to_item': 'good_detail_page_view_time_list_softsearch'},\n", " {'from_item': 'good_click_top100_850',\n", "  'to_item': 'good_click_origin_price_list_softsearch'},\n", " {'from_item': 'good_click_top100_851',\n", "  'to_item': 'good_click_label_list_softsearch'}]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["t = [\n", "    (\"good_click_item_id_list_top100\", 840, \"good_click_item_id_list_top100\"), \n", "    (\"good_click_seller_id_list_top100\", 841, \"good_click_seller_id_list_top100\"),\n", "    (\"good_click_real_seller_id_list_top100\", 842, \"good_click_real_seller_id_list_top100\"),\n", "    (\"good_click_lag_list_top100\", 843, \"good_click_lag_list_top100\"),\n", "    (\"good_click_carry_type_list_top100\", 844, \"good_click_carry_type_list_top100\"),\n", "    (\"good_click_click_type_list_top100\", 845, \"good_click_click_type_list_top100\"),\n", "    (\"good_click_category_list_top100\", 846, \"good_click_category_list_top100\"),\n", "    (\"good_click_from_list_top100\", 848, \"good_click_from_list_top100\"),\n", "    (\"good_click_price_list_top100\", 848, \"good_click_price_list_top100\"),\n", "    (\"good_detail_page_view_time_list_top100\", 849, \"good_detail_page_view_time_list_top100\"),\n", "    (\"good_click_origin_price_list_top100\", 850, \"good_click_origin_price_list_top100\"),\n", "    (\"good_click_label_list_top100\", 851, \"good_click_label_list_top100\"),\n", "]\n", "res = []\n", "for a,b,c in t:\n", "    res.append({'from_item': f\"good_click_top100_{b}\", 'to_item':c.replace('_top100', '_softsearch')})\n", "res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def transformer_layer_splithead(self,layer_input, extra_query_inputs, user_complex_actions, invalid_mask_kv, trans_output=16, nh=8, topk=100, name=\"\"):\n", "        # layer_input: [batch_size, x]\n", "        # user_complex_actions: [batch_size, join_limit, sum(dim_i)]\n", "        with tf.variable_scope(name + \"_layers\", reuse=tf.AUTO_REUSE):\n", "            if layer_input is None:\n", "                layer_input = extra_query_inputs\n", "            elif extra_query_inputs is not None:\n", "                layer_input = tf.concat([tf.stop_gradient(layer_input), extra_query_inputs], axis=-1)\n", "            else:\n", "                layer_input = tf.stop_gradient(layer_input)\n", "            rown = tf.shape(layer_input)[0]\n", "            coln = layer_input.get_shape()[1]\n", "\n", "            query_input = tf.reshape(layer_input, (rown, 1, coln))\n", "\n", "            colm_kv = user_complex_actions.get_shape()[-1]\n", "            sq_query = query_input.shape.as_list()[1]\n", "            sq_value = user_complex_actions.shape.as_list()[1]\n", "\n", "            Q = tf.get_variable('q_trans_matrix', (coln, trans_output * nh))\n", "            K = tf.get_variable('k_trans_matrix', (colm_kv, trans_output * nh))\n", "            V = tf.get_variable('v_trans_matrix', (colm_kv, trans_output * nh))\n", "            attn_mask = invalid_mask_kv * (-1000.0)  # [batch_size, sq_len]\n", "            \n", "            # [batch_size, 1, dim * heads]\n", "            querys = tf.tensordot(query_input, Q, axes=(-1, 0))\n", "            querys_trans = tf.transpose(tf.reshape(querys, [-1, sq_query, nh, trans_output]), [0, 2, 1, 3])\n", "            # [batch_size, 50, dim * heads]\n", "            keys = tf.tensordot(user_complex_actions, K, axes=(-1, 0))\n", "            keys_trans =  tf.transpose(tf.reshape(keys, [-1, sq_value, nh, trans_output]), [0, 2, 1, 3])\n", "            # [batch_size, 50, dim * heads]\n", "            values = tf.tensordot(user_complex_actions, V, axes=(-1, 0))\n", "            values_trans = tf.transpose(tf.reshape(values, [-1, sq_value, nh, trans_output]), [0, 2, 1, 3])\n", "            \n", "            Q_mat_K_score = tf.matmul(querys_trans,keys_trans,transpose_b=True)  # (batch_size, nh, 1, sq_len)\n", "\n", "            ####### mask\n", "            attn_mask = tf.tile(tf.expand_dims(tf.expand_dims(attn_mask, 1),2), [1, 8, 1, 1] )\n", "\n", "            Q_mat_K_score = Q_mat_K_score + attn_mask  # (h, b, l1, l)\n", "            Q_mat_K_score = tf.clip_by_value(Q_mat_K_score, -10000.0, 10000.0)\n", "\n", "            score = tf.reshape(Q_mat_K_score, [-1, nh, sq_value]) / 8.0\n", "\n", "            _, h_idx = tf.math.top_k(score, topk)  # (bs, nh, topk)\n", "            h_select_values = tf.gather(values_trans, h_idx, batch_dims=2)  # (bs, nh, topk,att_emb_size)\n", "\n", "            inner_product = tf.gather(score, h_idx, batch_dims=2)  # (batch_size, nh, topk)\n", "            normalized_att_scores = tf.nn.softmax(inner_product)  # (batch_size,nh,topk)\n", "            normalized_att_scores = tf.reshape(normalized_att_scores, [rown, nh, 1, topk])\n", "\n", "            result = tf.matmul(normalized_att_scores, h_select_values)  # (batch_size,nh,1, att_emb_size)\n", "            mha_result = tf.reshape(result, (-1, nh * trans_output))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["18"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["text = \"\"\"class=ExtractItemGoodsBasicAttr, category=photo, size=10000001, field=354, slot=150, topic_id=0\n", "class=ExtractItemGoodsGender, category=photo, size=10001, field=355, slot=151, topic_id=0\n", "class=ExtractItemGoodsPeople, category=photo, size=5000001, field=356, slot=152, topic_id=0\n", "class=ExtractItemGoodsSeason, category=photo, size=1001, field=357, slot=153, topic_id=0\n", "class=ExtractItemGoodsAge, category=photo, size=50001, field=358, slot=154, topic_id=0\n", "class=ExtractItemGoodsPlace, category=photo, size=100001, field=359, slot=155, topic_id=0\n", "class=ExtractItemGoodsEnter, category=photo, size=11, field=360, slot=156, topic_id=0\n", "class=ExtractItemGoodsPrice, category=photo, size=101, field=361, slot=157, topic_id=0\n", "class=ExtractItemGoodsScene, category=photo, size=100001, field=362, slot=158, topic_id=0\n", "class=ExtractItemGoodsBrand, category=photo, size=1000001, field=363, slot=159, topic_id=0\n", "class=ExtractItemGoodsEffect, category=photo, size=1000001, field=364, slot=160, topic_id=0\n", "class=ExtractItemGoodsStyle, category=photo, size=10000001, field=365, slot=161, topic_id=0\n", "class=ExtractItemGoodsCloth, category=photo, size=1000001, field=366, slot=162, topic_id=0\n", "class=ExtractItemGoodsMedical, category=photo, size=1000001, field=367, slot=163, topic_id=0\n", "class=ExtractItemGoodsFood, category=photo, size=1000001, field=368, slot=164, topic_id=0\n", "class=ExtractItemGoodsMakeup, category=photo, size=1000001, field=369, slot=165, topic_id=0\n", "class=ExtractItemGoodsBuild, category=photo, size=1000001, field=370, slot=166, topic_id=0\n", "class=ExtractItemGoodsConsume, category=photo, size=1000001, field=371, slot=167, topic_id=0\"\"\".splitlines()\n", "\n", "a = [t.split(',')[0].split('=')[1].lower() for t in text]\n", "# ', '.join(a)\n", "len(a)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["[4401757007868,\n", " 4401757007868,\n", " 20468670355750,\n", " 4011751484868,\n", " 4011751484868,\n", " 4011751484868,\n", " 22013309198318,\n", " 22013309198318,\n", " 22013309198318,\n", " 22740844189621]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["b = [729, 731, 632, 733, 735, 734, 126, 127, 35, 564]\n", "[a[i] for i in b]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}