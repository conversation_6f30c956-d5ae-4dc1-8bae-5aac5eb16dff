good_click_topk_indices_dp (int_list[100]): [44, 43, 8, 4, 7, 5, 6, 42, 3, 2, 9, 31, 30, 27, 49, 29, 12, 13, 10, 11, 26, 41, 1, 0, 16, 25, 21, 50, 15, 22, 28, 33, 48, 23, 20, 36, 35, 37, 14, 32, 45, 24, 17, 19, 34, 47, 40, 39, 38, 64, 66, 65, 68, 61, 60, 67, 63, 57, 62, 59, 54, 55, 58, 56, 96, 97, 98, 99, 100, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 112, 113, 114, 115]
  good_click_topk_values_dp (double_list[100]): [0.305908, 0.305908, 0.252686, 0.252686, 0.246582, 0.246582, 0.246582, 0.243652, 0.237427, 0.237427, 0.237427, 0.223389, 0.223389, 0.212769, 0.195801, 0.190918, 0.18396, 0.18396, 0.18396, 0.18396, 0.180908, 0.178467, 0.175659, 0.175659, 0.168945, 0.155518, 0.14209, 0.13855, 0.128784, 0.124939, 0.12323, 0.0998535, 0.0982666, 0.0979004, 0.0946045, 0.090271, 0.090271, 0.090271, 0.076355, 0.0706177, 0.0578918, 0.0541077, 0.0274658, 0.0266113, 0.0249329, 0.0199127, 0.000782013, 0.000782013, 0.000782013, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  good_click_topk_indices (int_list[54]): [43, 44, 4, 8, 5, 6, 7, 42, 2, 3, 9, 30, 31, 27, 49, 29, 10, 11, 12, 13, 26, 41, 0, 1, 16, 25, 21, 50, 15, 22, 28, 33, 48, 23, 20, 35, 36, 37, 14, 32, 45, 24, 17, 19, 34, 47, 38, 39, 40, 18, 46, 51, 52, 53]
  good_click_topk_values (double_list[54]): [0.305908, 0.305908, 0.252686, 0.252686, 0.246582, 0.246582, 0.246582, 0.243652, 0.237427, 0.237427, 0.237427, 0.223389, 0.223389, 0.212769, 0.195801, 0.190918, 0.18396, 0.18396, 0.18396, 0.18396, 0.180908, 0.178467, 0.175659, 0.175659, 0.168945, 0.155518, 0.14209, 0.13855, 0.128784, 0.124939, 0.12323, 0.0998535, 0.0982666, 0.0979004, 0.0946045, 0.090271, 0.090271, 0.090271, 0.076355, 0.0706177, 0.0578918, 0.0541077, 0.0274658, 0.0266113, 0.0249329, 0.0199127, 0.000782013, 0.000782013, 0.000782013, 0, 0, 0, 0, 0]
  non_zero_value_len (int): 49

# 特征size都是1001
# combine
combine = """ExtractCombineRankIndexUescorePctrNoPrefix
ExtractCombineRankIndexUescorePlvtrNoPrefix
ExtractCombineRankIndexUescorePsvrNoPrefix
ExtractCombineRankIndexUescorePvtrNoPrefix
ExtractCombineRankIndexUescorePwatchtimeNoPrefix
ExtractCombineRankIndexUescorePwtdNoPrefix
ExtractCombineRankIndexUescorePcprNoPrefix
ExtractCombineRankIndexUescorePltrNoPrefix
ExtractCombineRankIndexUescorePwtrNoPrefix
ExtractCombineRankIndexUescorePftrNoPrefix
ExtractCombineRankIndexUescorePcmtrNoPrefix
ExtractCombineRankIndexUescorePhtrNoPrefix
ExtractCombineRankIndexUescorePclickLiveNoPrefix
ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix
ExtractCombineRankIndexUescorePptrNoPrefix
ExtractCombineRankIndexUescorePepstrNoPrefix
ExtractCombineRankIndexUescorePlstrNoPrefix
ExtractCombineRankIndexUescorePetcmNoPrefix
ExtractCombineRankIndexUescorePcmefNoPrefix
ExtractCombineRankIndexInnerOrderCvrNoPrefix""".splitlines()
user = """ExtractUserRankInnerOrderNum
ExtractUserRankUescoreNum""".splitlines()
len(combine) + len(user)
print(',\n'.join(combine + user))

for i in combine:
    print(f"column_name={i}, compression_type=None, column_type=List<Long>, feature_extractor_flag = true")
for i in user:
    print(f"column_name={i}, compression_type=None, column_type=List<Long>, feature_extractor_flag = true")

for i in combine:
    print(f"{i} array<bigint>,")
for i in user:
    print(f"{i} array<bigint>,")

start_slot = 303

slot = start_slot
for i in combine:
    size = 1001
    category = 'combine'
    # concat_len = 50 * dim
    print(f"class={i}, category={category}, field=0, slot={slot}, size={size}, topic_id=0")
    slot += 1

start_slot = 301
slot = start_slot
for i in user:
    size = 1001
    category = 'user'
    print(f"class={i}, category={category}, field=0, slot={slot}, size={size}, topic_id=0")
    slot += 1

remap_start_slot = 667

slot = remap_start_slot
for i in combine:
    size = 1001
    dim=16
    category = 'combine'
    print(f"remap_slot={slot}, dim={dim}, map_slot={slot}, size=0, slot=0, name={i}, category=ad, field=0, feature_size={size}, topic_id=0")
    slot += 1

remap_start_slot = 665
slot = remap_start_slot
for i in user:
    size = 1001
    dim=16
    category = 'user'
    print(f"remap_slot={slot}, dim={dim}, map_slot={slot}, size=0, slot=0, name={i}, category=ad, field=0, feature_size={size}, topic_id=0")
    slot += 1